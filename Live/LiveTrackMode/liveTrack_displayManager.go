package livetrackmode

import (
	"fmt" // Nécessaire pour Sprintf et Sscanf
	"log"
	"math" // Nécessaire pour Round
	communication "oscbridge/Communication"
	"oscbridge/Live/utils"
	"strings" // Nécessaire pour shortString et Join
)

// LiveTrackDisplayManager gère l'affichage du mode Track
type LiveTrackDisplayManager struct {
	communicationManager *communication.CommunicationManager
	volumeConverter      *utils.VolumeConverter
	volumeSendConverter  *utils.VolumeSendConverter
	parentMode           *LiveTrackMode // Référence au mode parent pour IsActive()
}

// NewLiveTrackDisplayManager crée une nouvelle instance de LiveTrackDisplayManager
func NewLiveTrackDisplayManager(commManager *communication.CommunicationManager, parentMode *LiveTrackMode) *LiveTrackDisplayManager {
	return &LiveTrackDisplayManager{
		communicationManager: commManager,
		volumeConverter:      utils.NewVolumeConverter(),
		volumeSendConverter:  utils.NewVolumeSendConverter(),
		parentMode:           parentMode,
	}
}

// shortString raccourcit une chaîne de caractères
func (m *LiveTrackDisplayManager) shortString(s string, maxLength int) string {
	if len(s) > maxLength {
		return s[:maxLength] + "..."
	}
	return s
}

// boolToInt convertit un booléen en entier (0 ou 1)
func boolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// ClearTrackDisplay efface l'affichage d'une piste (st0, ts)
// Cette méthode est utilisée lors de la désactivation du mode Track
func (m *LiveTrackDisplayManager) ClearTrackDisplay() {
	if m.communicationManager == nil {
		return
	}

	// Ne pas effacer la sélection de piste (st0) lors du changement de mode
	// car cela peut interférer avec l'activation du mode suivant
	// Nous effaçons uniquement les slots de sends

	// Effacer les 4 slots de sends
	for i := 0; i < 4; i++ {
		slotLetter := string(rune('A' + i))
		// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
		m.communicationManager.SendMessage(fmt.Sprintf("ts%s,0,---", slotLetter), true)
	}
}

// UpdateSendsDisplay met à jour l'affichage des sends (ts)
func (m *LiveTrackDisplayManager) UpdateSendsDisplay(sends []float64, currentPage int, sendsPerPage int) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Créer une map pour stocker temporairement les messages à envoyer
	// Cela nous permettra d'éviter les envois dupliqués
	sendMessages := make(map[string]string)

	startSendIndex := (currentPage - 1) * sendsPerPage
	for i := 0; i < sendsPerPage; i++ {
		sendIndex := startSendIndex + i
		slotLetter := string(rune('A' + i))

		var message string
		if sendIndex < len(sends) {
			sendValue := sends[sendIndex]
			var sendDB string
			if m.volumeSendConverter != nil {
				sendDB = m.volumeSendConverter.ToDb(sendValue)
			} else {
				sendDB = "-inf dB" // Valeur par défaut
			}
			message = fmt.Sprintf("ts%s,%d,%s dB", slotLetter, int(math.Round(sendValue*100)), sendDB)
		} else {
			message = fmt.Sprintf("ts%s,0,---", slotLetter)
		}

		// Stocker le message dans la map avec la clé basée sur le slot
		sendMessages[slotLetter] = message
	}

	// Envoyer les messages dans l'ordre des slots
	for i := 0; i < sendsPerPage; i++ {
		slotLetter := string(rune('A' + i))
		if message, exists := sendMessages[slotLetter]; exists {
			m.communicationManager.SendMessage(message, isActive)
		}
	}
}

// UpdateSingleSend met à jour l'affichage d'un seul send
func (m *LiveTrackDisplayManager) UpdateSingleSend(slotLetter string, value float64) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Calculer la valeur en dB
	var sendDB string
	if m.volumeSendConverter != nil {
		sendDB = m.volumeSendConverter.ToDb(value)
	} else {
		sendDB = "-inf dB" // Valeur par défaut
	}

	// Créer et envoyer le message pour ce send uniquement
	message := fmt.Sprintf("ts%s,%d,%s dB", slotLetter, int(math.Round(value*100)), sendDB)
	m.communicationManager.SendMessage(message, isActive)
}

// ClearSend réinitialise l'affichage d'un slot send
func (m *LiveTrackDisplayManager) ClearSend(slotLetter string) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Envoyer un message pour effacer ce send
	m.communicationManager.SendMessage(fmt.Sprintf("ts%s,0,---", slotLetter), isActive)
}

// ClearAllSends réinitialise l'affichage de tous les slots sends (A, B, C, D)
func (m *LiveTrackDisplayManager) ClearAllSends() {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Effacer tous les slots sends
	for i := 0; i < SendsPerPage; i++ {
		slotLetter := string(rune('A' + i))
		m.communicationManager.SendMessage(fmt.Sprintf("ts%s,0,---", slotLetter), isActive)
	}
}

// UpdateReturnNames met à jour l'affichage des noms de retours (rn)
func (m *LiveTrackDisplayManager) UpdateReturnNames(returnNames []string, currentPage int, sendsPerPage int) {
	if m.communicationManager == nil {
		log.Println("UpdateReturnNames: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	} else {
		log.Println("UpdateReturnNames: parentMode est nil, utilisation de isActive=true par défaut")
	}

	startIndex := (currentPage - 1) * sendsPerPage
	pageNames := make([]string, sendsPerPage)
	for i := range pageNames {
		pageNames[i] = "nul" // Valeur par défaut comme dans le JS
	}

	for i := 0; i < sendsPerPage; i++ {
		nameIndex := startIndex + i
		if nameIndex < len(returnNames) && returnNames[nameIndex] != "" {
			pageNames[i] = m.shortString(returnNames[nameIndex], 12)
		}
	}

	message := fmt.Sprintf("rn,%s", strings.Join(pageNames, ","))
	log.Printf("UpdateReturnNames: Envoi du message '%s'", message)
	m.communicationManager.SendMessage(message, isActive)
}

// UpdateVolumeDisplay met à jour l'affichage du volume (vt)
func (m *LiveTrackDisplayManager) UpdateVolumeDisplay(value float64, dbValue string) {
	if m.communicationManager == nil {
		log.Println("UpdateVolumeDisplay: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	message := fmt.Sprintf("vt%d,%s", int(math.Round(value*100)), dbValue)
	m.communicationManager.SendMessage(message, isActive)
}

// UpdatePanningDisplay met à jour l'affichage du panoramique (pan)
func (m *LiveTrackDisplayManager) UpdatePanningDisplay(value float64) {
	if m.communicationManager == nil {
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	mappedPanning := int(math.Round(value * 50))
	m.communicationManager.SendMessage(fmt.Sprintf("pan%d", mappedPanning), isActive)
}

// UpdateTrackViewState met à jour l'affichage de l'état de la vue (quickview/lock)
// state:
// 0 = quickview
// 1 = !quickview & !islocked
// 2 = !quickview & islocked
func (m *LiveTrackDisplayManager) UpdateTrackViewState(isQuickView bool, isLocked bool) {
	if m.communicationManager == nil {
		log.Println("UpdateTrackViewState: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Déterminer l'état à envoyer
	var state int
	if isQuickView {
		state = 0 // Mode QuickView (le lock n'a pas d'importance)
	} else if isLocked {
		state = 2 // Mode normal verrouillé
	} else {
		state = 1 // Mode normal non verrouillé
	}

	message := fmt.Sprintf("tq%d", state)
	m.communicationManager.SendMessage(message, isActive)
}

// UpdateModeDisplay met à jour l'affichage du mode (mo)
func (m *LiveTrackDisplayManager) UpdateModeDisplay(mode string) {
	if m.communicationManager == nil {
		return
	}

	// Ne pas envoyer de message si le mode est "None"
	if mode == "None" {
		log.Println("UpdateModeDisplay: Ignoring 'None' mode to prevent ESP32 errors")
		return
	}

	// Forcer l'envoi avec priorité pour s'assurer que les mises à jour sont visibles
	m.communicationManager.SendMessage(fmt.Sprintf("mo,%s", mode), true)
}

// UpdateColorDisplay met à jour l'affichage de la couleur (ct)
func (m *LiveTrackDisplayManager) UpdateColorDisplay(color string) {
	if m.communicationManager == nil {
		log.Println("UpdateColorDisplay: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	var colorDec int
	if len(color) > 0 {
		// La couleur est déjà stockée comme entier décimal exact sous forme de chaîne
		_, err := fmt.Sscanf(color, "%d", &colorDec)
		if err != nil {
			log.Printf("UpdateColorDisplay: Erreur lors de la conversion de la couleur %s: %v", color, err)
			colorDec = 0 // Utiliser la valeur par défaut
		}
	}

	message := fmt.Sprintf("ct%d", colorDec)
	m.communicationManager.SendMessage(message, isActive)
}

// UpdateNameDisplay met à jour l'affichage du nom (nt)
func (m *LiveTrackDisplayManager) UpdateNameDisplay(name string) {
	if m.communicationManager == nil {
		log.Println("UpdateNameDisplay: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}
	if !m.parentMode.isQuickView && m.parentMode.isLocked {
		name = "Locked on " + name
	}

	message := fmt.Sprintf("nt,%s", name)
	m.communicationManager.SendMessage(message, isActive)
}

// UpdateArmDisplay met à jour l'affichage de l'état d'armement (ta)
func (m *LiveTrackDisplayManager) UpdateArmDisplay(arm int) {
	if m.communicationManager == nil {
		log.Println("UpdateArmDisplay: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Le JS utilise '1' pour armé, '0' pour désarmé.
	message := fmt.Sprintf("ta%d", arm)
	m.communicationManager.SendMessage(message, isActive)
}

func (m *LiveTrackDisplayManager) UpdateMuteDisplay(mute int) {
	if m.communicationManager == nil {
		log.Println("UpdateMuteDisplay: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Le JS utilise '1' pour armé, '0' pour désarmé.
	message := fmt.Sprintf("mt,%d", mute)
	m.communicationManager.SendMessage(message, isActive)

}

func (m *LiveTrackDisplayManager) UpdateMuteViaSoloDisplay(muteViaSolo int) {
	if m.communicationManager == nil {
		log.Println("UpdateMuteViaSoloDisplay: communicationManager est nil")
		return
	}
	//Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Le JS utilise '1' pour armé, '0' pour désarmé.
	message := fmt.Sprintf("tv,%d", muteViaSolo)
	m.communicationManager.SendMessage(message, isActive)

}

func (m *LiveTrackDisplayManager) UpdateSoloDisplay(solo int) {
	if m.communicationManager == nil {
		log.Println("UpdateSoloDisplay: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Le JS utilise '1' pour armé, '0' pour désarmé.
	message := fmt.Sprintf("to,%d", solo)
	m.communicationManager.SendMessage(message, isActive)

}

// --- Méthodes pour les relations entre pistes ---

// HandleTrackDaughters gère les messages OSC pour les pistes filles
func (m *LiveTrackDisplayManager) HandleTrackDaughters(daughterNames []string) {
	if m.communicationManager == nil {
		log.Println("HandleTrackDaughters: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Limiter à 64 pistes maximum
	trackCount := len(daughterNames)
	if trackCount > 64 {
		log.Printf("HandleTrackDaughters: Limitation à 64 pistes (reçu %d)", trackCount)
		daughterNames = daughterNames[:64]
		trackCount = 64
	}

	// Envoyer d'abord le nombre total de pistes filles
	m.communicationManager.SendMessage(fmt.Sprintf("tc,%d", trackCount), isActive)
	log.Printf("HandleTrackDaughters: Envoi du nombre total de pistes filles: %d", trackCount)

	// Envoyer les noms par lots de 8
	for i := 0; i < trackCount; i += 8 {
		// Calculer la fin du lot (min entre i+8 et trackCount)
		end := i + 8
		if end > trackCount {
			end = trackCount
		}

		// Construire le message avec l'index de départ et les noms
		message := fmt.Sprintf("td,%d", i)
		for j := i; j < end; j++ {
			message += "," + m.shortString(daughterNames[j], 12)
		}

		// Envoyer le message
		m.communicationManager.SendMessage(message, isActive)
		log.Printf("HandleTrackDaughters: Envoi du lot %d-%d: %s", i, end-1, message)
	}
}

// HandleTrackSisters gère les messages OSC pour les pistes soeurs
func (m *LiveTrackDisplayManager) HandleTrackSisters(sisterNames []string) {
	if m.communicationManager == nil {
		log.Println("HandleTrackSisters: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Limiter à 64 pistes maximum
	trackCount := len(sisterNames)
	if trackCount > 64 {
		log.Printf("HandleTrackSisters: Limitation à 64 pistes (reçu %d)", trackCount)
		sisterNames = sisterNames[:64]
		trackCount = 64
	}

	// Envoyer d'abord le nombre total de pistes soeurs
	m.communicationManager.SendMessage(fmt.Sprintf("tw,%d", trackCount), isActive)
	log.Printf("HandleTrackSisters: Envoi du nombre total de pistes soeurs: %d", trackCount)

	// Envoyer les noms par lots de 8
	for i := 0; i < trackCount; i += 8 {
		// Calculer la fin du lot (min entre i+8 et trackCount)
		end := i + 8
		if end > trackCount {
			end = trackCount
		}

		// Construire le message avec l'index de départ et les noms
		message := fmt.Sprintf("tx,%d", i)
		for j := i; j < end; j++ {
			message += "," + m.shortString(sisterNames[j], 12)
		}

		// Envoyer le message
		m.communicationManager.SendMessage(message, isActive)
		log.Printf("HandleTrackSisters: Envoi du lot %d-%d: %s", i, end-1, message)
	}
}

// HandleTrackParent gère les messages OSC pour la piste parente
func (m *LiveTrackDisplayManager) HandleTrackParent(parentName string) {
	if m.communicationManager == nil {
		log.Println("HandleTrackParent: communicationManager est nil")
		return
	}

	// Vérifier si parentMode est nil pour éviter une panique
	isActive := true
	if m.parentMode != nil {
		isActive = m.parentMode.IsActive()
	}

	// Envoyer simplement le nom de la piste parente
	message := fmt.Sprintf("tp,%s", m.shortString(parentName, 12))
	m.communicationManager.SendMessage(message, isActive)
	log.Printf("HandleTrackParent: Envoi de la piste parente: %s", message)
}

// HandleSyncMessage gère l'affichage des messages de synchronisation
func (m *LiveTrackDisplayManager) HandleSyncMessage(bar, beat, subdivision int, time, tempo float32, signatureNum, signatureDenom, isPlaying int) {
	if m.communicationManager == nil {
		return
	}

	// Envoyer le message formaté sp,<bar>,<beat>,<subdivision>,<time>,<tempo>,<signature_num>,<signature_denom>,<isplaying>
	message := fmt.Sprintf("sp,%d,%d,%d,%.2f,%.2f,%d,%d,%d", bar, beat, subdivision, time, tempo, signatureNum, signatureDenom, isPlaying)
	m.communicationManager.SendMessage(message, true)
	log.Printf("HandleSyncMessage: Envoi du message de sync: %s", message)
}

// HandleBeatsMessage gère l'affichage des messages de beats
func (m *LiveTrackDisplayManager) HandleBeatsMessage(bar, beat, subdivision int, time float32) {
	if m.communicationManager == nil {
		return
	}

	// Envoyer le message formaté sb,<bar>,<beat>,<subdivision>,<time>
	message := fmt.Sprintf("sb,%d,%d,%d,%.2f", bar, beat, subdivision, time)
	m.communicationManager.SendMessage(message, true)
	log.Printf("HandleBeatsMessage: Envoi du message de beats: %s", message)
}

// HandleSongStateMessage gère les messages OSC pour l'état du morceau et met à jour l'affichage.
func (m *LiveTrackDisplayManager) HandleSongStateMessage(isPlaying, recordMode, metronome, loop, punchIn, punchOut int) {
	if m.communicationManager == nil {
		log.Println("HandleSongStateMessage: communicationManager est nil")
		return
	}

	// Le message attendu est ss,<isplaying><Record_Mode><Metronome><Loop><PunchIn><PunchOut>
	// Où chaque paramètre est 0 ou 1
	message := fmt.Sprintf("ss,%d%d%d%d%d%d",
		isPlaying,
		recordMode,
		metronome,
		loop,
		punchIn,
		punchOut,
	)

	// Envoyer le message. Priorité élevée pour s'assurer que l'état est toujours visible.
	m.communicationManager.SendMessage(message, true) // Toujours envoyer l'état du morceau, même si le mode n'est pas actif
	log.Printf("HandleSongStateMessage: Envoi de l'état du morceau: %s", message)
}

// UpdateCueNameDisplay met à jour l'affichage du nom de la cue (sc)
func (m *LiveTrackDisplayManager) UpdateCueNameDisplay(name string) {
	if m.communicationManager == nil {
		log.Println("UpdateCueNameDisplay: communicationManager est nil")
		return
	}

	// Le message attendu est sc,<name>
	message := fmt.Sprintf("sc,%s", m.shortString(name, 20)) // Limite la longueur du nom si nécessaire

	// Envoyer le message. Priorité élevée pour s'assurer que l'état est toujours visible.
	// Utiliser `isActive` si le message ne doit être envoyé que si le mode est actif,
	// sinon forcer l'envoi avec `true`. Pour le nom de la cue, on peut le forcer.
	m.communicationManager.SendMessage(message, true)
	log.Printf("UpdateCueNameDisplay: Envoi du nom de la cue: %s", message)
}

// HandleCuePointNames gère l'affichage des noms de cue points
func (m *LiveTrackDisplayManager) HandleCuePointNames(cuePointNames []string) {
	if m.communicationManager == nil {
		log.Println("HandleCuePointNames: communicationManager est nil")
		return
	}

	// Limiter à 64 cue points maximum
	cuePointCount := len(cuePointNames)
	if cuePointCount > 64 {
		log.Printf("HandleCuePointNames: Limitation à 64 cue points (reçu %d)", cuePointCount)
		cuePointNames = cuePointNames[:64]
		cuePointCount = 64
	}

	// Envoyer d'abord le nombre total de cue points
	countMessage := fmt.Sprintf("cn,%d", cuePointCount)
	m.communicationManager.SendMessage(countMessage, true)
	log.Printf("HandleCuePointNames: Envoi du nombre total de cue points: %s", countMessage)

	// Envoyer les noms par lots de 8 maximum
	batchSize := 8
	for i := 0; i < cuePointCount; i += batchSize {
		// Calculer la fin du lot (min entre i+batchSize et cuePointCount)
		end := i + batchSize
		if end > cuePointCount {
			end = cuePointCount
		}

		// Calculer le nombre d'éléments dans ce lot
		batchCount := end - i

		// Construire le message avec le nombre d'éléments, l'index de départ et les noms
		message := fmt.Sprintf("c1,%d,%d", batchCount, i)
		for j := i; j < end; j++ {
			// Raccourcir les noms si nécessaire pour éviter les messages trop longs
			shortName := m.shortString(cuePointNames[j], 12)
			message += "," + shortName
		}

		// Envoyer le message
		m.communicationManager.SendMessage(message, true)
		log.Printf("HandleCuePointNames: Envoi du lot %d-%d: %s", i, end-1, message)
	}
}

// RefreshFullTrackUIDisplay met à jour l'intégralité de l'affichage pour la piste donnée.
func (dm *LiveTrackDisplayManager) RefreshFullTrackUIDisplay(trackData *TrackData, currentPage int, isActive bool, returnNames []string) {
	if dm.communicationManager == nil {
		log.Println("RefreshFullTrackUIDisplay: communicationManager non initialisé")
		return
	}
	if trackData == nil {
		log.Println("RefreshFullTrackUIDisplay: trackData est nil. Effacement de l'affichage.")
		dm.ClearTrackDisplay() // Appelle une fonction pour effacer l'affichage de la piste
		return
	}

	// S'assurer que les valeurs par défaut sont présentes pour éviter les paniques
	if trackData.DisplayIndex == "" {
		trackData.DisplayIndex = "?" // Ou une autre valeur par défaut appropriée
	}
	if trackData.FormattedTrackNumber == "" {
		trackData.FormattedTrackNumber = "track ?/?" // Ou une autre valeur par défaut
	}
	if dm.volumeConverter == nil { // Vérification ajoutée
		log.Println("RefreshFullTrackUIDisplay: volumeConverter non initialisé")
		// Gérer l'erreur, peut-être utiliser une valeur par défaut pour VolumeDb
		// ou retourner pour éviter une panique. Pour l'instant, on logge et continue.
	}

	// 1. Construction et envoi du message SLT (basé sur la logique de updateOptimizedTrackDisplay)
	var flagPrefix string
	// Assurez-vous que BoolToInt est accessible (ex: livetrackmode.BoolToInt ou une fonction locale)
	// Pour cet exemple, je vais supposer une fonction locale `boolToIntDisplay` ou que vous l'avez rendue accessible.
	// Temporairement, je vais la définir ici pour que l'exemple soit complet, mais elle devrait être partagée.
	/* boolToIntDisplay := func(b bool) int { // Utiliser la fonction boolToInt du package
		if b {
			return 1
		}
		return 0
	}*/

	if strings.Contains(trackData.Name, "Return") || strings.HasPrefix(trackData.Name, "A-") {
		flagPrefix = fmt.Sprintf("slt%d%d%d%d%d%d", 0, 0, 0, 0, 0, 2) // Foldable, FoldState, IsGrouped, Mute, Solo, SpecialReturnFlag
	} else if strings.Contains(trackData.Name, "Main") {
		// Mute, Solo, Arm, MutedViaSolo pour Main
		flagPrefix = fmt.Sprintf("slt%d%d%d%d%d%d%d", 0, 0, 0, 4, 2, 2, 2)
	} else {
		// Pistes régulières
		flagPrefix = fmt.Sprintf("slt%d%d%d%d%d%d%d",
			boolToInt(trackData.TrackIsFoldable),
			boolToInt(trackData.TrackFoldState),
			boolToInt(trackData.TrackIsGrouped),
			boolToInt(trackData.Mute),
			boolToInt(trackData.Solo),
			boolToInt(trackData.MutedViaSolo),
			trackData.Arm)
	}

	var colorDec int
	if len(trackData.Color) > 0 {
		fmt.Sscanf(trackData.Color, "%d", &colorDec)
	}
	volumePercent := int(math.Round(trackData.Volume * 100))
	volumeDb := ""
	if dm.volumeConverter != nil {
		volumeDb = dm.volumeConverter.ToDb(trackData.Volume)
	}

	NameToSend := ""
	if !dm.parentMode.isQuickView && dm.parentMode.isLocked && dm.parentMode.isActive {
		NameToSend = "Locked on " + trackData.Name
	} else {
		NameToSend = trackData.Name
	}

	sltMessage := fmt.Sprintf("%s,%s,%d,%s,%s,%d,%s",
		flagPrefix,
		trackData.DisplayIndex,
		colorDec,
		dm.shortString(NameToSend, 25), // Assumant que shortString est une méthode de dm
		trackData.FormattedTrackNumber,
		volumePercent,
		volumeDb,
	)
	dm.communicationManager.SendMessage(sltMessage, isActive)

	// 2. Mettre à jour le panoramique
	dm.UpdatePanningDisplay(trackData.Panning)

	// 3. Mettre à jour les sends et les noms des retours associés
	// La structure TrackDisplayInfo n'est plus nécessaire ici si les arguments sont passés directement.
	/* displayInfo := &TrackDisplayInfo{ // Supposant que TrackDisplayInfo n'est pas défini ou nécessaire ici
		MuteState:         boolToInt(trackData.Mute),
		SoloState:         boolToInt(trackData.Solo),
		MutedViaSoloState: boolToInt(trackData.MutedViaSolo),
		VolumeDb:          volumeDb,
		CurrentPage:       currentPage,
		SendsPerPage:      SendsPerPage, // Assurez-vous que SendsPerPage est accessible
	} */

	// 3. Initialiser les sends avec des valeurs vides (les vraies valeurs viendront via les messages OSC individuels)
	// Cela évite les doublons car on n'envoie que l'état initial vide
	dm.ClearAllSends()
	dm.UpdateReturnNames(returnNames, currentPage, SendsPerPage) // Met à jour les noms des retours pour la page actuelle

	// 4. Mettre à jour les pistes relatives (Parent, Daughters, Sisters)
	dm.HandleTrackParent(trackData.ParentName)
	dm.HandleTrackDaughters(trackData.DaughterNames)
	dm.HandleTrackSisters(trackData.SisterNames)

	// 5. Mettre à jour les affichages individuels si nécessaire (déjà couverts par SLT ou autres appels)
	// Ces appels sont généralement pour des mises à jour granulaires.
	// Si le message SLT et les appels ci-dessus suffisent, pas besoin de les dupliquer.
	// dm.UpdateNameDisplay(dm.shortString(trackData.Name, 20)) // Déjà dans SLT
	// dm.UpdateColorDisplay(trackData.Color) // Déjà dans SLT
	// dm.UpdateVolumeDisplay(trackData.Volume, volumeDb) // Déjà dans SLT
	// dm.UpdateMuteDisplay(boolToInt(trackData.Mute)) // Déjà dans SLT
	// dm.UpdateSoloDisplay(boolToInt(trackData.Solo)) // Déjà dans SLT
	// dm.UpdateMuteViaSoloDisplay(boolToInt(trackData.MutedViaSolo)) // Déjà dans SLT
	// dm.UpdateArmDisplay(trackData.Arm) // Déjà dans SLT
}
