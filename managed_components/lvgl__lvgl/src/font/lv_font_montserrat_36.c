/*******************************************************************************
 * Size: 36 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 36 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_36.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_36
    #define LV_FONT_MONTSERRAT_36 1
#endif

#if LV_FONT_MONTSERRAT_36

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x2f, 0xff, 0xd0, 0x2f, 0xff, 0xd0, 0x1f, 0xff,
    0xc0, 0xf, 0xff, 0xb0, 0xf, 0xff, 0xb0, 0xf,
    0xff, 0xa0, 0xf, 0xff, 0x90, 0xe, 0xff, 0x90,
    0xd, 0xff, 0x80, 0xd, 0xff, 0x70, 0xc, 0xff,
    0x70, 0xc, 0xff, 0x60, 0xb, 0xff, 0x50, 0xa,
    0xff, 0x50, 0xa, 0xff, 0x40, 0x9, 0xff, 0x30,
    0x9, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x76, 0x0, 0x2f,
    0xff, 0xc0, 0x7f, 0xff, 0xf1, 0x5f, 0xff, 0xf0,
    0x8, 0xed, 0x40,

    /* U+0022 "\"" */
    0xbf, 0xf4, 0x0, 0x3f, 0xfc, 0xbf, 0xf4, 0x0,
    0x3f, 0xfc, 0xaf, 0xf3, 0x0, 0x2f, 0xfb, 0xaf,
    0xf3, 0x0, 0x2f, 0xfb, 0xaf, 0xf2, 0x0, 0x1f,
    0xfb, 0x9f, 0xf2, 0x0, 0x1f, 0xfa, 0x9f, 0xf2,
    0x0, 0x1f, 0xfa, 0x9f, 0xf1, 0x0, 0xf, 0xf9,
    0x8f, 0xf1, 0x0, 0xf, 0xf9, 0x49, 0x90, 0x0,
    0x9, 0x95,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0,
    0x7, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfa, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80, 0x0,
    0x0, 0xb, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x30,
    0x0, 0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf1, 0x0, 0x0, 0x1, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x89, 0x99, 0x9f,
    0xfe, 0x99, 0x99, 0x99, 0xdf, 0xf9, 0x99, 0x99,
    0x30, 0x0, 0x0, 0x0, 0xff, 0x90, 0x0, 0x0,
    0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf7, 0x0, 0x0, 0x0, 0xbf, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x50, 0x0,
    0x0, 0xd, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x0, 0x1f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0x0, 0x3, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfd,
    0x0, 0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x9, 0x99, 0x9a, 0xff, 0xc9, 0x99, 0x99,
    0x9e, 0xfe, 0x99, 0x99, 0x91, 0x0, 0x0, 0x0,
    0x2f, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40, 0x0,
    0x0, 0xf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf2, 0x0, 0x0, 0x0, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0,
    0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x4, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfc,
    0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0, 0x0, 0x0,
    0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x84, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x8c, 0xef, 0xff,
    0xed, 0xa6, 0x10, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0xdf, 0xff, 0xc6, 0x2c, 0xf9, 0x24, 0x8d,
    0xff, 0x40, 0x6, 0xff, 0xf9, 0x0, 0xc, 0xf8,
    0x0, 0x0, 0x4a, 0x0, 0xb, 0xff, 0xe0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xa0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xb0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf2, 0x0, 0xc, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfe, 0x60,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfe, 0xae, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xfe, 0x95, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0, 0x27,
    0xbf, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfc, 0xcf, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x2, 0x9f,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8,
    0x0, 0x5, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0xcf, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0xaf, 0xfd,
    0x4, 0x30, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0xef, 0xfb, 0xc, 0xf9, 0x10, 0x0, 0xc, 0xf8,
    0x0, 0x9, 0xff, 0xf6, 0x4f, 0xff, 0xfa, 0x62,
    0x1c, 0xf8, 0x15, 0xcf, 0xff, 0xd0, 0x1b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0x0, 0x37, 0xbd, 0xff, 0xff,
    0xec, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8,
    0x0, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x5, 0xbe, 0xfd, 0x81, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xfc, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x20, 0x0, 0x0, 0x8, 0xff, 0x71,
    0x4, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x60, 0x0, 0x0, 0x1, 0xff, 0x80, 0x0, 0x1,
    0xff, 0x70, 0x0, 0x0, 0x1, 0xef, 0xb0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0, 0x0, 0x0,
    0x9, 0xfc, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0,
    0x0, 0x6f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0x0, 0x4, 0xff, 0x0, 0x0, 0x2f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0xc, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x9, 0xfc, 0x0, 0x7, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x70, 0x0, 0x1, 0xff,
    0x70, 0x2, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x71, 0x4, 0xdf, 0xe0, 0x0,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x8f, 0xf4,
    0x0, 0x5c, 0xef, 0xd7, 0x0, 0x0, 0x0, 0x6,
    0xcf, 0xfe, 0x91, 0x0, 0x3f, 0xf8, 0x0, 0xaf,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfd, 0x0, 0x7f, 0xf8, 0x11,
    0x5e, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x30, 0xf, 0xf7, 0x0, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x70, 0x5, 0xff, 0x0, 0x0, 0x0, 0xcf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xc0, 0x0,
    0x9f, 0xc0, 0x0, 0x0, 0x8, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf6, 0x0, 0x0, 0xaf, 0xa0, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xfb, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0xb, 0xfe, 0x10,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0xb, 0xfa,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xff, 0x50, 0x0, 0x2, 0xff, 0x50, 0x0,
    0x0, 0x2, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0x2, 0xdf, 0xc0, 0x0, 0x0, 0x0,
    0xcf, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xed, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x7f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xfd,
    0x70, 0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x18, 0xce, 0xfe, 0xc7, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xea, 0x89, 0xef, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfb,
    0x0, 0x0, 0xc, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf2, 0x0, 0x0, 0x4, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0,
    0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf1, 0x0, 0x0, 0x8, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9,
    0x0, 0x0, 0x5f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x60, 0x9, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf8, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf9,
    0x2b, 0xff, 0xf7, 0x0, 0x0, 0xb, 0x83, 0x0,
    0x0, 0xcf, 0xfe, 0x40, 0x0, 0xbf, 0xff, 0x80,
    0x0, 0x2f, 0xfc, 0x0, 0x8, 0xff, 0xf2, 0x0,
    0x0, 0xb, 0xff, 0xf8, 0x0, 0x7f, 0xf8, 0x0,
    0xf, 0xff, 0x60, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x90, 0xef, 0xf3, 0x0, 0x4f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfc, 0xff, 0xc0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xfe, 0x0, 0x0,
    0x1f, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xff, 0xff, 0xb0, 0x0, 0x8, 0xff, 0xfe, 0x61,
    0x0, 0x0, 0x4a, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xed, 0xef, 0xff, 0xff,
    0xd2, 0x7f, 0xff, 0xc0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0x0, 0x6, 0xff, 0x90,
    0x0, 0x0, 0x5, 0xad, 0xef, 0xed, 0xa5, 0x0,
    0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xbf, 0xf4, 0xbf, 0xf4, 0xaf, 0xf3, 0xaf, 0xf3,
    0xaf, 0xf2, 0x9f, 0xf2, 0x9f, 0xf2, 0x9f, 0xf1,
    0x8f, 0xf1, 0x49, 0x90,

    /* U+0028 "(" */
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x2, 0xff, 0xf2,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x2f, 0xff, 0x30,
    0x0, 0x8f, 0xfd, 0x0, 0x0, 0xef, 0xf7, 0x0,
    0x3, 0xff, 0xf2, 0x0, 0x7, 0xff, 0xe0, 0x0,
    0xb, 0xff, 0xa0, 0x0, 0xe, 0xff, 0x70, 0x0,
    0x1f, 0xff, 0x40, 0x0, 0x4f, 0xff, 0x20, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x8f, 0xfe, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0,
    0x9f, 0xfc, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0,
    0x8f, 0xfe, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0x5f, 0xff, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0,
    0x1f, 0xff, 0x40, 0x0, 0xe, 0xff, 0x70, 0x0,
    0xb, 0xff, 0xa0, 0x0, 0x7, 0xff, 0xd0, 0x0,
    0x2, 0xff, 0xf2, 0x0, 0x0, 0xcf, 0xf7, 0x0,
    0x0, 0x7f, 0xfd, 0x0, 0x0, 0x1f, 0xff, 0x30,
    0x0, 0x9, 0xff, 0xa0, 0x0, 0x1, 0xff, 0xf2,
    0x0, 0x0, 0x8f, 0xfb,

    /* U+0029 ")" */
    0x9f, 0xfb, 0x0, 0x0, 0x1f, 0xff, 0x40, 0x0,
    0x8, 0xff, 0xc0, 0x0, 0x1, 0xff, 0xf4, 0x0,
    0x0, 0xbf, 0xfa, 0x0, 0x0, 0x5f, 0xff, 0x10,
    0x0, 0xf, 0xff, 0x50, 0x0, 0xc, 0xff, 0xa0,
    0x0, 0x8, 0xff, 0xd0, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x1, 0xff, 0xf4, 0x0, 0x0, 0xff, 0xf6,
    0x0, 0x0, 0xdf, 0xf8, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0xdf, 0xf8, 0x0, 0x0, 0xff, 0xf6,
    0x0, 0x1, 0xff, 0xf4, 0x0, 0x4, 0xff, 0xf1,
    0x0, 0x8, 0xff, 0xd0, 0x0, 0xb, 0xff, 0xa0,
    0x0, 0xf, 0xff, 0x40, 0x0, 0x5f, 0xfe, 0x0,
    0x0, 0xbf, 0xf9, 0x0, 0x1, 0xff, 0xf4, 0x0,
    0x8, 0xff, 0xc0, 0x0, 0x1f, 0xff, 0x30, 0x0,
    0x9f, 0xfb, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x4, 0x40,
    0x0, 0xdf, 0x40, 0x1, 0x70, 0xd, 0xfb, 0x20,
    0xdf, 0x40, 0x8f, 0xf4, 0x1b, 0xff, 0xf9, 0xef,
    0x9e, 0xff, 0xe4, 0x0, 0x4c, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x19, 0xff, 0xff, 0xff, 0xc3, 0x0,
    0x7, 0xff, 0xfc, 0xef, 0xbf, 0xff, 0xb2, 0x1f,
    0xfe, 0x60, 0xdf, 0x42, 0xbf, 0xf6, 0x6, 0x80,
    0x0, 0xdf, 0x40, 0x4, 0xb0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x10,
    0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x8, 0x88, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x87, 0xdd, 0xdd, 0xdd,
    0xff, 0xfd, 0xdd, 0xdd, 0xd7, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x4, 0xcd, 0x60, 0x1f, 0xff, 0xf3, 0x3f, 0xff,
    0xf7, 0x1f, 0xff, 0xf5, 0x3, 0xef, 0xf1, 0x0,
    0xff, 0xb0, 0x3, 0xff, 0x60, 0x7, 0xff, 0x0,
    0xb, 0xfb, 0x0, 0xf, 0xf5, 0x0,

    /* U+002D "-" */
    0xef, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+002E "." */
    0x3, 0xcc, 0x50, 0x1f, 0xff, 0xf3, 0x4f, 0xff,
    0xf7, 0x2f, 0xff, 0xf4, 0x5, 0xee, 0x70,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0x87,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x0, 0x6, 0xae, 0xff, 0xeb, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xb5, 0x33, 0x5a,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf3, 0x0, 0x0,
    0xcf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfc, 0x0, 0x3, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x30, 0x9, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x90,
    0xd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0xf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf0, 0x2f, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf2, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf3, 0x4f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf4, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf3, 0x2f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf2, 0xf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0xd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0x9, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x90, 0x3, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x30, 0x0, 0xcf, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xfc, 0x0, 0x0, 0x3f, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xf3, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xa5, 0x33, 0x5a, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xbe, 0xff, 0xeb, 0x60,
    0x0, 0x0, 0x0,

    /* U+0031 "1" */
    0xbf, 0xff, 0xff, 0xff, 0xf8, 0xbf, 0xff, 0xff,
    0xff, 0xf8, 0xbf, 0xff, 0xff, 0xff, 0xf8, 0x11,
    0x11, 0x11, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0xff, 0xf8,

    /* U+0032 "2" */
    0x0, 0x0, 0x5, 0xac, 0xef, 0xfe, 0xb7, 0x10,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x3f, 0xff,
    0xfe, 0x95, 0x32, 0x36, 0xbf, 0xff, 0xf6, 0x0,
    0x9, 0xff, 0x80, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xfe, 0x0, 0x0, 0x75, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xe3, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x10, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7,

    /* U+0033 "3" */
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x12, 0xdf, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfc, 0x63, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xfe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x22,
    0x33, 0x6a, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x45, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xfa, 0xe, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0x48,
    0xff, 0xff, 0xc8, 0x53, 0x23, 0x5a, 0xff, 0xff,
    0xb0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x7, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x48, 0xbd,
    0xef, 0xed, 0xa5, 0x10, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xe1,
    0x0, 0x0, 0x48, 0x87, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf4, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x2, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xfd, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0x0,

    /* U+0035 "5" */
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x9,
    0xff, 0xc1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0,
    0x0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x31, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xc9, 0x40, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x7d, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf4, 0x0, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf1,
    0x8, 0xfd, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xb0, 0x2f, 0xff, 0xfe, 0xa6, 0x42, 0x34,
    0x8e, 0xff, 0xff, 0x30, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x3b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0,
    0x0, 0x0, 0x16, 0xad, 0xef, 0xfd, 0xc8, 0x30,
    0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x1, 0x6a, 0xdf, 0xff, 0xeb,
    0x82, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0x94, 0x21, 0x13, 0x6b,
    0xd0, 0x0, 0x0, 0xd, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x70, 0x3, 0x8c, 0xef, 0xed,
    0x94, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x30, 0x0, 0x4f, 0xff,
    0x6d, 0xff, 0xff, 0xee, 0xff, 0xff, 0xff, 0x40,
    0x3, 0xff, 0xff, 0xff, 0xc5, 0x0, 0x0, 0x4b,
    0xff, 0xff, 0x20, 0x2f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xfa, 0x1, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf0,
    0xe, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x20, 0xaf, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf3, 0x5, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x20,
    0xe, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xe0, 0x0, 0x6f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf8, 0x0, 0x0, 0xbf, 0xff,
    0xc5, 0x0, 0x0, 0x3a, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xee, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x16,
    0xad, 0xef, 0xec, 0x82, 0x0, 0x0, 0x0,

    /* U+0037 "7" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xef, 0xf9,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x3f, 0xff, 0x90,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x20, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfb, 0x0, 0xef, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x89, 0x94,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x1, 0x6a, 0xde, 0xfe, 0xdb, 0x72,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xee, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0xb, 0xff, 0xfe, 0x61, 0x0, 0x1, 0x5d, 0xff,
    0xfd, 0x0, 0x2, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf5, 0x0, 0x6f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0x7,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfa, 0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x80, 0x1, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x8,
    0xff, 0xfb, 0x20, 0x0, 0x0, 0x29, 0xff, 0xfb,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xeb, 0xbb, 0xdf,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x1a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,
    0x0, 0x1d, 0xff, 0xfd, 0x73, 0x0, 0x2, 0x6c,
    0xff, 0xfe, 0x20, 0xa, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xfc, 0x1, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf3,
    0x5f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x76, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf9, 0x5f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x82,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf5, 0xc, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0x0, 0x3f, 0xff, 0xfb,
    0x51, 0x0, 0x0, 0x4a, 0xff, 0xff, 0x60, 0x0,
    0x5f, 0xff, 0xff, 0xfe, 0xee, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x2, 0x7b,
    0xde, 0xfe, 0xdb, 0x73, 0x0, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x16, 0xbd, 0xff, 0xdb, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xee, 0xff, 0xff, 0xfd, 0x10, 0x0, 0xa, 0xff,
    0xfc, 0x50, 0x0, 0x4, 0xaf, 0xff, 0xd0, 0x0,
    0x4f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xf9, 0x0, 0xaf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x20, 0xef, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xe0,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xf1, 0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf4, 0x6f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xf6, 0xd, 0xff,
    0xfd, 0x50, 0x0, 0x3, 0xaf, 0xff, 0xff, 0xf7,
    0x2, 0xef, 0xff, 0xff, 0xee, 0xff, 0xff, 0xf5,
    0xff, 0xf7, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x32, 0xff, 0xf6, 0x0, 0x0, 0x38, 0xce,
    0xfe, 0xd9, 0x40, 0x3, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf2, 0x0, 0x0, 0xbc, 0x73, 0x10, 0x13, 0x7d,
    0xff, 0xff, 0x50, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x17, 0xbd, 0xff, 0xfd, 0xb7, 0x20, 0x0,
    0x0, 0x0,

    /* U+003A ":" */
    0x5, 0xee, 0x70, 0x2f, 0xff, 0xf4, 0x4f, 0xff,
    0xf6, 0x1f, 0xff, 0xf3, 0x4, 0xcc, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xcc, 0x50, 0x1f, 0xff, 0xf3,
    0x4f, 0xff, 0xf7, 0x2f, 0xff, 0xf4, 0x5, 0xee,
    0x70,

    /* U+003B ";" */
    0x5, 0xee, 0x70, 0x2f, 0xff, 0xf4, 0x4f, 0xff,
    0xf6, 0x1f, 0xff, 0xf3, 0x4, 0xcc, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xcd, 0x60, 0x1f, 0xff, 0xf3,
    0x3f, 0xff, 0xf7, 0x1f, 0xff, 0xf5, 0x3, 0xef,
    0xf1, 0x0, 0xff, 0xb0, 0x3, 0xff, 0x60, 0x7,
    0xff, 0x0, 0xb, 0xfb, 0x0, 0xf, 0xf5, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x17,
    0xd8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xaf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x5b, 0xff, 0xff,
    0xfd, 0x71, 0x0, 0x0, 0x28, 0xef, 0xff, 0xff,
    0xa3, 0x0, 0x0, 0x6, 0xcf, 0xff, 0xff, 0xc6,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xe8, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xc6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0xef, 0xff,
    0xff, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b,
    0xff, 0xff, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x7d, 0xff, 0xff, 0xfb, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xaf, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xdf, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20,

    /* U+003D "=" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x89, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x7c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7d, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0x79, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+003E ">" */
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xc6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xfd, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x8d, 0xff, 0xff, 0xfb, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xff, 0xff,
    0xe8, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6, 0xcf,
    0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x28, 0xef, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xcf, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xe8, 0x10, 0x0, 0x1,
    0x7d, 0xff, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x5b,
    0xff, 0xff, 0xfd, 0x71, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xe9, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x16, 0xad, 0xff, 0xfe, 0xb7, 0x10,
    0x0, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x4, 0xff, 0xff, 0xd7,
    0x31, 0x1, 0x4a, 0xff, 0xff, 0x80, 0x2c, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x0,
    0x7, 0x40, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x17, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6e, 0xe6, 0x0, 0x0, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0xbe,
    0xef, 0xfe, 0xca, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf,
    0xff, 0xfd, 0xa7, 0x65, 0x56, 0x8b, 0xff, 0xff,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfe, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xcf, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0x10,
    0x0, 0x0, 0x4, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfb, 0x0, 0x0, 0x1, 0xef, 0xf2, 0x0, 0x0,
    0x0, 0x5a, 0xef, 0xfd, 0x94, 0x0, 0x7f, 0xfa,
    0x0, 0x8f, 0xf6, 0x0, 0x0, 0x8f, 0xf6, 0x0,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xfb, 0x27,
    0xff, 0xa0, 0x0, 0xcf, 0xe0, 0x0, 0xf, 0xfd,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xfd, 0xcd, 0xff,
    0xfe, 0x9f, 0xfa, 0x0, 0x4, 0xff, 0x60, 0x5,
    0xff, 0x60, 0x0, 0x2, 0xff, 0xfe, 0x60, 0x0,
    0x1, 0x8f, 0xff, 0xff, 0xa0, 0x0, 0xd, 0xfb,
    0x0, 0xbf, 0xf0, 0x0, 0x0, 0xdf, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xfa, 0x0, 0x0,
    0x7f, 0xf0, 0xe, 0xfc, 0x0, 0x0, 0x4f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xa0,
    0x0, 0x4, 0xff, 0x31, 0xff, 0x90, 0x0, 0x9,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfa, 0x0, 0x0, 0x1f, 0xf6, 0x2f, 0xf7, 0x0,
    0x0, 0xcf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xa0, 0x0, 0x0, 0xff, 0x73, 0xff,
    0x60, 0x0, 0xd, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfa, 0x0, 0x0, 0xf, 0xf7,
    0x3f, 0xf6, 0x0, 0x0, 0xdf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0,
    0xff, 0x72, 0xff, 0x70, 0x0, 0xc, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfa, 0x0,
    0x0, 0x1f, 0xf6, 0x1f, 0xf9, 0x0, 0x0, 0x9f,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xa0, 0x0, 0x3, 0xff, 0x30, 0xef, 0xc0, 0x0,
    0x4, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xfa, 0x0, 0x0, 0x7f, 0xf0, 0xa, 0xff,
    0x10, 0x0, 0xc, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xb0, 0x0, 0xd, 0xfb, 0x0,
    0x5f, 0xf6, 0x0, 0x0, 0x2f, 0xff, 0xe6, 0x0,
    0x0, 0x17, 0xff, 0xef, 0xff, 0x30, 0x8, 0xff,
    0x40, 0x0, 0xff, 0xd0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xcb, 0xdf, 0xff, 0xe1, 0xef, 0xff, 0xcd,
    0xff, 0xb0, 0x0, 0x8, 0xff, 0x70, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x5, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x1e, 0xff, 0x20,
    0x0, 0x0, 0x5, 0xae, 0xff, 0xda, 0x40, 0x0,
    0x4, 0xbe, 0xfc, 0x70, 0x0, 0x0, 0x0, 0x4f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe7,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x28, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b,
    0xff, 0xff, 0xda, 0x76, 0x66, 0x8a, 0xdf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x15, 0x9c, 0xef, 0xff,
    0xec, 0x95, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc, 0x7f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf5, 0x1f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xe0, 0x9, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x70, 0x2, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0xbf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf9, 0x0, 0x0, 0x4f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf2, 0x0, 0x0, 0xd, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xa0,
    0x0, 0x0, 0x6, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xef, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x1, 0xff, 0xfe,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdf, 0xff,
    0x60, 0x0, 0x0, 0x8, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0x0,
    0x0, 0xe, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf5, 0x0, 0x0, 0x6f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfc, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x30, 0x5, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xb0,
    0xc, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf2,

    /* U+0042 "B" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xda,
    0x50, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x3f,
    0xff, 0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xff,
    0xf9, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xaf, 0xff, 0xf5, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xc0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0x0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf1,
    0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xd0, 0x3,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf6, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x2, 0x5c, 0xff, 0xfb, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0x0, 0x3, 0xff, 0xfe,
    0xee, 0xee, 0xee, 0xee, 0xef, 0xff, 0xff, 0xf4,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8f, 0xff, 0xf4, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xd0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x33, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x63, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf5, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0x13, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0xef, 0xff, 0x90, 0x3f, 0xff, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xff, 0xff, 0xff, 0xc0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xed, 0xa6, 0x10, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xc7, 0x43, 0x24, 0x6b, 0xff, 0xff, 0xf4,
    0x0, 0x5, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0xd1, 0x0, 0x2f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0x10,
    0x0, 0xcf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0x10,
    0x0, 0x6, 0xff, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0xff, 0xd2, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xc7, 0x43, 0x24, 0x6b, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0,

    /* U+0044 "D" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xb8,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x3f,
    0xff, 0x61, 0x11, 0x11, 0x11, 0x24, 0x8d, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0x30,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xe1, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x10,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x60, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xa0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xd0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xf0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x60, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0x10, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf9, 0x0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xe1, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0x30,
    0x0, 0x3f, 0xff, 0x61, 0x11, 0x11, 0x11, 0x24,
    0x8d, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xb8,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x3f, 0xff,
    0x61, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x61, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x10, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1,

    /* U+0046 "F" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x93, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x93, 0xff, 0xf6, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x3, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xca, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xc8, 0x53, 0x24, 0x5a, 0xef, 0xff, 0xf8,
    0x0, 0x5, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xef, 0xf3, 0x0, 0x2f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2b, 0x30,
    0x0, 0xbf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7a, 0xa7, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfb, 0xe, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0x9, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfb, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0x0, 0xcf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfb, 0x0, 0x2f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0x0, 0x5, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xef, 0xfb, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xc8, 0x43, 0x23, 0x59, 0xef, 0xff, 0xfa,
    0x0, 0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x7,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xda, 0x61, 0x0, 0x0,

    /* U+0048 "H" */
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x73, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x73, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x73,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf7, 0x3f, 0xff, 0x61, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x3f, 0xff, 0x73, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x73, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x73, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x73, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x70,

    /* U+0049 "I" */
    0x3f, 0xff, 0x63, 0xff, 0xf6, 0x3f, 0xff, 0x63,
    0xff, 0xf6, 0x3f, 0xff, 0x63, 0xff, 0xf6, 0x3f,
    0xff, 0x63, 0xff, 0xf6, 0x3f, 0xff, 0x63, 0xff,
    0xf6, 0x3f, 0xff, 0x63, 0xff, 0xf6, 0x3f, 0xff,
    0x63, 0xff, 0xf6, 0x3f, 0xff, 0x63, 0xff, 0xf6,
    0x3f, 0xff, 0x63, 0xff, 0xf6, 0x3f, 0xff, 0x63,
    0xff, 0xf6, 0x3f, 0xff, 0x63, 0xff, 0xf6, 0x3f,
    0xff, 0x63, 0xff, 0xf6, 0x3f, 0xff, 0x60,

    /* U+004A "J" */
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x1, 0x11, 0x11, 0x11, 0x11, 0xbf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfc,
    0x0, 0x93, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf9,
    0x8, 0xfe, 0x30, 0x0, 0x0, 0xa, 0xff, 0xf5,
    0x1f, 0xff, 0xfa, 0x41, 0x14, 0xbf, 0xff, 0xe0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x4a, 0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+004B "K" */
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfa, 0x3, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xfb, 0x0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xfb, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xfc, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfc, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x5, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x3, 0xff, 0xf6, 0x0, 0x0, 0x4, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x4, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf6, 0x0, 0x4, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x4, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf6, 0x3, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x63, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0x68, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x60, 0xa, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x60, 0x0, 0xb, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x60, 0x0,
    0x0, 0xd, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xf6,
    0x0, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xf4, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf2,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xd1, 0x0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xb0,
    0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x90, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x60,

    /* U+004C "L" */
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf6, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x10, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0,

    /* U+004D "M" */
    0x3f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf9, 0x3f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf9, 0x3f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xf9, 0x3f, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf9,
    0x3f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf9, 0x3f, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf9, 0x3f, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xf9, 0x3f, 0xff, 0xaf, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xfa, 0xef, 0xf9,
    0x3f, 0xff, 0x3c, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf1, 0xef, 0xf9, 0x3f, 0xff,
    0x33, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0x70, 0xef, 0xf9, 0x3f, 0xff, 0x30, 0x9f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0x0,
    0xef, 0xf9, 0x3f, 0xff, 0x30, 0x1e, 0xff, 0x90,
    0x0, 0x0, 0x2, 0xff, 0xf4, 0x0, 0xef, 0xf9,
    0x3f, 0xff, 0x30, 0x6, 0xff, 0xf3, 0x0, 0x0,
    0xc, 0xff, 0xa0, 0x0, 0xef, 0xf9, 0x3f, 0xff,
    0x30, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0x5f, 0xff,
    0x10, 0x0, 0xef, 0xf9, 0x3f, 0xff, 0x30, 0x0,
    0x2f, 0xff, 0x60, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0xef, 0xf9, 0x3f, 0xff, 0x30, 0x0, 0x9, 0xff,
    0xe1, 0x8, 0xff, 0xd0, 0x0, 0x0, 0xef, 0xf9,
    0x3f, 0xff, 0x30, 0x0, 0x0, 0xef, 0xf9, 0x2f,
    0xff, 0x40, 0x0, 0x0, 0xef, 0xf9, 0x3f, 0xff,
    0x30, 0x0, 0x0, 0x5f, 0xff, 0xcf, 0xfa, 0x0,
    0x0, 0x0, 0xef, 0xf9, 0x3f, 0xff, 0x30, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0xdf, 0xf9, 0x3f, 0xff, 0x30, 0x0, 0x0, 0x2,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0xdf, 0xf9,
    0x3f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x8f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9, 0x3f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf9, 0x3f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf9, 0x3f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9,
    0x3f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9,

    /* U+004E "N" */
    0x3f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x73, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x73, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x73, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff, 0xdf,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x73, 0xff, 0xf6, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf7, 0x3f, 0xff, 0x60, 0xdf,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x73,
    0xff, 0xf6, 0x2, 0xef, 0xff, 0x70, 0x0, 0x0,
    0x2, 0xff, 0xf7, 0x3f, 0xff, 0x60, 0x4, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x2f, 0xff, 0x73, 0xff,
    0xf6, 0x0, 0x7, 0xff, 0xfe, 0x20, 0x0, 0x2,
    0xff, 0xf7, 0x3f, 0xff, 0x60, 0x0, 0xa, 0xff,
    0xfd, 0x0, 0x0, 0x2f, 0xff, 0x73, 0xff, 0xf6,
    0x0, 0x0, 0xc, 0xff, 0xfa, 0x0, 0x2, 0xff,
    0xf7, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x1e, 0xff,
    0xf7, 0x0, 0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xf5, 0x2, 0xff, 0xf7,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xf2, 0x2f, 0xff, 0x73, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xd3, 0xff, 0xf7, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xf7, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0x73, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf7, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x73, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf7, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x70,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xca, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xc7,
    0x43, 0x23, 0x6b, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xf5, 0x0, 0x0, 0xbf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfe, 0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x70,
    0x9, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xd0, 0xe, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf2, 0x1f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf5, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf7,
    0x4f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf7, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf7, 0x1f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf5, 0xe, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf2,
    0x9, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xd0, 0x3, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x70, 0x0, 0xbf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfe, 0x10, 0x0, 0x2f, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf5, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x19, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xc7, 0x43, 0x23, 0x6a, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xda, 0x61, 0x0, 0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x83,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x3f, 0xff, 0x61, 0x11, 0x11, 0x12, 0x47,
    0xdf, 0xff, 0xf7, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x20, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x90, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xe0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf1, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xe0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x90, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x30, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x25, 0xbf, 0xff,
    0xf8, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x94,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x61, 0x11, 0x11,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xca, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xfc, 0x74, 0x32, 0x46, 0xbf, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0, 0x0,
    0xbf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0x0, 0x0, 0x3f, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xf7, 0x0, 0x9, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xd0, 0x0, 0xef, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x20, 0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x3,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x70, 0x4f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf7, 0x3, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x70, 0x1f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf5, 0x0, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x20,
    0xa, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0, 0x4f,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf8, 0x0, 0x0, 0xdf, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x10, 0x0, 0x3, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff,
    0x60, 0x0, 0x0, 0x7, 0xff, 0xff, 0xb1, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xfa, 0x52, 0x11,
    0x24, 0x9e, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xbe, 0xff, 0xff, 0xfe, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x6,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xfb, 0x30, 0x0, 0x3a, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xff, 0xff, 0xed, 0xef, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x8c, 0xff,
    0xeb, 0x60, 0x0,

    /* U+0052 "R" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x83,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x3f, 0xff, 0x61, 0x11, 0x11, 0x12, 0x47,
    0xdf, 0xff, 0xf7, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x20, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x90, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xe0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf1, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xe0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x90, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x20, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x25, 0xbf, 0xff,
    0xf8, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x70, 0x0, 0x0, 0x3f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf2, 0x0,
    0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xfc, 0x0, 0x0, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x70, 0x0, 0x3f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf2, 0x0, 0x3f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfd, 0x0, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x80,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf3,

    /* U+0053 "S" */
    0x0, 0x0, 0x1, 0x7b, 0xdf, 0xff, 0xeb, 0x84,
    0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe8, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xdf,
    0xff, 0xd7, 0x31, 0x1, 0x25, 0x9e, 0xff, 0x40,
    0x6, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0x0, 0xb, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xfe, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfe,
    0x95, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xfd, 0x95, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4c, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x26, 0xbf, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x36, 0xbf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfd, 0x5, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfb,
    0xd, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf5, 0x5f, 0xff, 0xfd, 0x84, 0x21, 0x1,
    0x37, 0xdf, 0xff, 0xc0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x3b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x15, 0x9c, 0xef, 0xff, 0xda, 0x61,
    0x0, 0x0,

    /* U+0054 "T" */
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x11, 0x11, 0x11, 0x14, 0xff, 0xf6, 0x11, 0x11,
    0x11, 0x11, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfe, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfe, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xfe, 0x7f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfe, 0x7f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfe, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfe, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xfe, 0x7f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x6f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfd, 0x5f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x4f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfb, 0xf, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf7, 0xc, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xf3, 0x6, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xd0, 0x0, 0xdf, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0x50, 0x0,
    0x3f, 0xff, 0xfe, 0x85, 0x32, 0x36, 0xcf, 0xff,
    0xf9, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x2b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x27, 0xbe, 0xff, 0xed, 0x95,
    0x0, 0x0, 0x0,

    /* U+0056 "V" */
    0xc, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x70, 0x5f, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xf0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf9,
    0x0, 0x7, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x1f,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf3,
    0x0, 0x0, 0x2, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfc, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x50,
    0x0, 0x0, 0x8, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xf4, 0x0, 0x0, 0x6f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xb0, 0x0, 0xd, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x20, 0x4,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf9, 0x0, 0xbf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xf1, 0x2f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x79,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfe, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0057 "W" */
    0xaf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x35, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0xf,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf8, 0x0, 0xaf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x30, 0x5, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xd0, 0x0, 0xf, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xaf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf8, 0x0, 0x0, 0xaf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xc3, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x30,
    0x0, 0x5, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf7, 0xd, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0x0, 0xf, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x10, 0x8f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf8, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xc0, 0x2, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x20, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0xef, 0xf6, 0x0, 0xd, 0xff, 0xa0,
    0x0, 0x0, 0x9, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xb0, 0x0, 0x0, 0x4f, 0xff, 0x10,
    0x0, 0x7f, 0xff, 0x0, 0x0, 0x0, 0xef, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x0, 0x0,
    0x9, 0xff, 0xb0, 0x0, 0x2, 0xff, 0xf5, 0x0,
    0x0, 0x4f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf5, 0x0, 0x0, 0xef, 0xf6, 0x0, 0x0,
    0xc, 0xff, 0xa0, 0x0, 0xa, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xb0, 0x0, 0x5f,
    0xff, 0x10, 0x0, 0x0, 0x7f, 0xff, 0x0, 0x0,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x10, 0xa, 0xff, 0xb0, 0x0, 0x0, 0x1,
    0xff, 0xf5, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf6, 0x0, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xa0, 0xa, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xb0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x0, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x1b, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf5, 0x5f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf7,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xba, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x9, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xb0, 0x0, 0xdf, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfe, 0x10,
    0x0, 0x2f, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf4, 0x0, 0x0, 0x6, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x70, 0x0, 0x0, 0x2,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xf3, 0x0, 0x0, 0xc, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfd, 0x0, 0x0, 0x8f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xa0, 0x4, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf6, 0x1e, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xcf, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf7, 0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0, 0x9, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xfe, 0x10, 0x0, 0xdf, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf4, 0x0, 0x0, 0x2f,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x90, 0x0, 0x0, 0x6, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x70, 0x0, 0x0, 0x1e, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xf3, 0x0,
    0x0, 0xaf, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xfd, 0x0, 0x6, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xa0,
    0x3f, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf6,

    /* U+0059 "Y" */
    0xd, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x20, 0x3f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x80, 0x0, 0x9f, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xd0, 0x0, 0x1, 0xef,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf4, 0x0, 0x0, 0x6, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfa, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x40, 0x0, 0x0, 0xb,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfd, 0x0, 0x0, 0x5, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf8, 0x0, 0x1,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf2, 0x0, 0x9f, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xb0,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x5c, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+005A "Z" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x12, 0xef, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfc, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x10, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9,

    /* U+005B "[" */
    0x3f, 0xff, 0xff, 0xff, 0x43, 0xff, 0xff, 0xff,
    0xf4, 0x3f, 0xff, 0xdd, 0xdd, 0x33, 0xff, 0xf3,
    0x0, 0x0, 0x3f, 0xff, 0x30, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x3f, 0xff, 0x30, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x3f, 0xff, 0x30, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x3f, 0xff, 0x30,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x3f, 0xff,
    0x30, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x3f,
    0xff, 0x30, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0,
    0x3f, 0xff, 0x30, 0x0, 0x3, 0xff, 0xf3, 0x0,
    0x0, 0x3f, 0xff, 0x30, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0x0, 0x3f, 0xff, 0x30, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x3f, 0xff, 0x30, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x3f, 0xff, 0x30, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x3f, 0xff, 0x30,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x3f, 0xff,
    0x30, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x3f,
    0xff, 0xdd, 0xdd, 0x33, 0xff, 0xff, 0xff, 0xf4,
    0x3f, 0xff, 0xff, 0xff, 0x40,

    /* U+005C "\\" */
    0x28, 0x86, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf7,

    /* U+005D "]" */
    0x5f, 0xff, 0xff, 0xff, 0x35, 0xff, 0xff, 0xff,
    0xf3, 0x4d, 0xdd, 0xdf, 0xff, 0x30, 0x0, 0x4,
    0xff, 0xf3, 0x0, 0x0, 0x4f, 0xff, 0x30, 0x0,
    0x4, 0xff, 0xf3, 0x0, 0x0, 0x4f, 0xff, 0x30,
    0x0, 0x4, 0xff, 0xf3, 0x0, 0x0, 0x4f, 0xff,
    0x30, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x0, 0x4f,
    0xff, 0x30, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x0,
    0x4f, 0xff, 0x30, 0x0, 0x4, 0xff, 0xf3, 0x0,
    0x0, 0x4f, 0xff, 0x30, 0x0, 0x4, 0xff, 0xf3,
    0x0, 0x0, 0x4f, 0xff, 0x30, 0x0, 0x4, 0xff,
    0xf3, 0x0, 0x0, 0x4f, 0xff, 0x30, 0x0, 0x4,
    0xff, 0xf3, 0x0, 0x0, 0x4f, 0xff, 0x30, 0x0,
    0x4, 0xff, 0xf3, 0x0, 0x0, 0x4f, 0xff, 0x30,
    0x0, 0x4, 0xff, 0xf3, 0x0, 0x0, 0x4f, 0xff,
    0x30, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x0, 0x4f,
    0xff, 0x30, 0x0, 0x4, 0xff, 0xf3, 0x0, 0x0,
    0x4f, 0xff, 0x30, 0x0, 0x4, 0xff, 0xf3, 0x4d,
    0xdd, 0xdf, 0xff, 0x35, 0xff, 0xff, 0xff, 0xf3,
    0x5f, 0xff, 0xff, 0xff, 0x30,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfd, 0xc, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x60, 0x6f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0, 0x0,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x1f, 0xf9, 0x0,
    0x9, 0xff, 0x20, 0x0, 0x0, 0x8, 0xff, 0x20,
    0x0, 0x2f, 0xf8, 0x0, 0x0, 0x0, 0xef, 0xc0,
    0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0, 0x6f, 0xf5,
    0x0, 0x0, 0x5, 0xff, 0x60, 0x0, 0xc, 0xfe,
    0x0, 0x0, 0x0, 0xe, 0xfc, 0x0, 0x3, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0xaf,
    0xf1, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x1f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x10,

    /* U+005F "_" */
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x38, 0x88, 0x60, 0x0, 0x0, 0x7, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xb0, 0x0, 0x0,
    0x1, 0xaf, 0xfc, 0x10, 0x0, 0x0, 0x6, 0xff,
    0xd1,

    /* U+0061 "a" */
    0x0, 0x0, 0x38, 0xbe, 0xff, 0xec, 0x82, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x1, 0xff, 0xe8, 0x30, 0x0,
    0x26, 0xef, 0xff, 0x70, 0x0, 0x67, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf5,
    0x0, 0x1, 0x7b, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0xff, 0xff, 0xb8, 0x77, 0x77,
    0x77, 0xff, 0xf6, 0xd, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf6, 0x1f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf6, 0x2f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf6, 0x1f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0xc,
    0xff, 0xe3, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xf6,
    0x3, 0xff, 0xff, 0xb8, 0x78, 0xcf, 0xfe, 0xff,
    0xf6, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0xef, 0xf6, 0x0, 0x1, 0x6b, 0xef, 0xfd, 0xa6,
    0x0, 0xef, 0xf6,

    /* U+0062 "b" */
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x2, 0x8c, 0xef, 0xec,
    0x83, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0xb, 0xff,
    0xcb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0xbf, 0xff, 0xff, 0xe8, 0x30, 0x2, 0x7d,
    0xff, 0xfe, 0x10, 0xb, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfb, 0x0, 0xbf, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf4,
    0xb, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x90, 0xbf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0xb, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xb, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xf0, 0xbf, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfd, 0xb,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x90, 0xbf, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf4, 0xb, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xfb, 0x0, 0xbf,
    0xff, 0xff, 0xe8, 0x30, 0x2, 0x7d, 0xff, 0xfe,
    0x10, 0xb, 0xff, 0x9b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0xbf, 0xf9, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x10, 0x0, 0xb, 0xff,
    0x90, 0x2, 0x8c, 0xef, 0xec, 0x83, 0x0, 0x0,
    0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x0, 0x49, 0xce, 0xff, 0xd9, 0x40,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x5, 0xff, 0xff,
    0xa4, 0x10, 0x14, 0xbf, 0xff, 0xe1, 0x2, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x5f, 0xf8, 0x0,
    0xaf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x52,
    0x0, 0xf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x52, 0x0, 0x2, 0xff, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x5f, 0xf9, 0x0, 0x4,
    0xff, 0xff, 0xa4, 0x10, 0x14, 0xaf, 0xff, 0xe1,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdf,
    0xff, 0xd9, 0x40, 0x0, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x40, 0x0, 0x0, 0x5, 0xad, 0xff, 0xdb,
    0x50, 0x3, 0xff, 0xf4, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xd4, 0x3f, 0xff, 0x40, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xf4, 0x0, 0x7f, 0xff, 0xfa, 0x41, 0x1, 0x5b,
    0xff, 0xff, 0xff, 0x40, 0x3f, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf4, 0xb, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0x41, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xf4, 0x4f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x46, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x7f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x46, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf4, 0x4f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x41,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf4, 0xb, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x40, 0x3f, 0xff, 0xd2,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xf4, 0x0,
    0x7f, 0xff, 0xf7, 0x10, 0x0, 0x18, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x8f, 0xff, 0xff, 0xec, 0xef,
    0xff, 0xf8, 0xff, 0xf4, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xf, 0xff, 0x40, 0x0,
    0x0, 0x6, 0xad, 0xff, 0xeb, 0x60, 0x0, 0xff,
    0xf4,

    /* U+0065 "e" */
    0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfe, 0xb6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xfe, 0xef, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x6f,
    0xff, 0xd5, 0x10, 0x0, 0x4c, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x40, 0xa, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xc0, 0x1f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf1, 0x4f, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf5,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x6f, 0xff, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x73, 0x4f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x2, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x7, 0xe2, 0x0, 0x0, 0x6f,
    0xff, 0xfb, 0x51, 0x0, 0x26, 0xdf, 0xfd, 0x10,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x70, 0x0, 0x0, 0x0, 0x0, 0x49,
    0xce, 0xff, 0xda, 0x60, 0x0, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x3a, 0xdf, 0xfd, 0x92, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x6f, 0xff, 0xfe, 0xdf, 0xf5, 0x0, 0x0, 0xef,
    0xfe, 0x30, 0x0, 0x50, 0x0, 0x2, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x5c,
    0xcd, 0xff, 0xfd, 0xcc, 0xcc, 0x80, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x0, 0x6a, 0xdf, 0xfe, 0xb6, 0x10,
    0xc, 0xff, 0x80, 0x0, 0x5, 0xef, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xcf, 0xf8, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xac, 0xff, 0x80,
    0x9, 0xff, 0xff, 0x94, 0x10, 0x3, 0x8f, 0xff,
    0xff, 0xf8, 0x5, 0xff, 0xfd, 0x20, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0x80, 0xdf, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xf8, 0x2f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x85, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf8, 0x7f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x87, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf8, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x82, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf8, 0xc, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0x80, 0x4f, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xf8, 0x0, 0x8f, 0xff, 0xfa, 0x41,
    0x1, 0x39, 0xff, 0xff, 0xff, 0x80, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xef, 0xf8,
    0x0, 0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xe, 0xff, 0x80, 0x0, 0x0, 0x6, 0xad, 0xff,
    0xeb, 0x71, 0x0, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf2, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xfe, 0x0, 0x7, 0xf9, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x70, 0x1,
    0xff, 0xff, 0xa6, 0x20, 0x0, 0x26, 0xdf, 0xff,
    0xe1, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x48, 0xbd, 0xef, 0xfe, 0xb8, 0x20, 0x0,
    0x0,

    /* U+0068 "h" */
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xb0, 0x3, 0x8c, 0xef,
    0xec, 0x81, 0x0, 0x0, 0xbf, 0xfb, 0x1a, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xb, 0xff, 0xcd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xbf,
    0xff, 0xff, 0xd6, 0x21, 0x14, 0xaf, 0xff, 0xf4,
    0xb, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xc0, 0xbf, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x1b, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf4, 0xbf, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x5b, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf6,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x6b, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf6, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x6b, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf6, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x6b,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf6, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x6b, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf6, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x6b, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf6,

    /* U+0069 "i" */
    0x0, 0x66, 0x10, 0xd, 0xff, 0xd0, 0x4f, 0xff,
    0xf4, 0x2f, 0xff, 0xf2, 0x6, 0xdd, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff,
    0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb,
    0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0,
    0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff,
    0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb,
    0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0,
    0xb, 0xff, 0xb0, 0xb, 0xff, 0xb0, 0xb, 0xff,
    0xb0,

    /* U+006A "j" */
    0x0, 0x0, 0x0, 0x0, 0x57, 0x20, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x4, 0xce, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xb0, 0x4, 0x60, 0x0, 0x9f,
    0xff, 0x60, 0xb, 0xff, 0xef, 0xff, 0xfe, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x5, 0xbe,
    0xff, 0xd8, 0x10, 0x0,

    /* U+006B "k" */
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfc, 0x0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xb0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfb, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xb0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0xb, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xbf, 0xfb, 0x0, 0x1, 0xcf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x2d, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x3, 0xef,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x4f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xfe, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xf7, 0xef, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x40,
    0x4f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xe3, 0x0, 0x6, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0xbf, 0xfe, 0x20, 0x0, 0x0, 0x9f, 0xff, 0xb0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf8, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0x50, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf2, 0x0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfd, 0x10, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xb0,

    /* U+006C "l" */
    0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb,
    0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb,
    0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb,
    0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb,
    0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb,
    0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb, 0xbf, 0xfb,
    0xbf, 0xfb, 0xbf, 0xfb,

    /* U+006D "m" */
    0xbf, 0xf9, 0x0, 0x5a, 0xdf, 0xfd, 0xb5, 0x0,
    0x0, 0x0, 0x5a, 0xde, 0xfe, 0xb6, 0x0, 0x0,
    0xbf, 0xf9, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0xbf, 0xfc, 0xef, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0x27, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0x92, 0x0, 0x3, 0xcf, 0xff,
    0xef, 0xff, 0xa3, 0x0, 0x3, 0xbf, 0xff, 0xd0,
    0xbf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf5,
    0xbf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xfa,
    0xbf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfd,
    0xbf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe,
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,

    /* U+006E "n" */
    0xbf, 0xf9, 0x0, 0x49, 0xde, 0xfe, 0xc8, 0x10,
    0x0, 0xb, 0xff, 0x92, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0xbf, 0xfb, 0xef, 0xff, 0xfe,
    0xef, 0xff, 0xff, 0x90, 0xb, 0xff, 0xff, 0xfa,
    0x30, 0x0, 0x17, 0xff, 0xff, 0x40, 0xbf, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0xb,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf1, 0xbf, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x4b, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf5, 0xbf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x6b, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf6, 0xbf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x6b, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf6, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x6b, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf6, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x6b, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf6,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x6b, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf6, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x60,

    /* U+006F "o" */
    0x0, 0x0, 0x0, 0x49, 0xdf, 0xff, 0xc9, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xa4, 0x10, 0x15, 0xbf, 0xff,
    0xf3, 0x0, 0x2, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xe1, 0x0, 0xaf, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x80, 0xf,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfe, 0x4, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf2, 0x6f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x47, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf5, 0x6f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x44, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0xf, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfe,
    0x0, 0xaf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x70, 0x2, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xe1, 0x0, 0x5, 0xff,
    0xff, 0xa4, 0x10, 0x15, 0xbf, 0xff, 0xf3, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xdf, 0xff, 0xc9, 0x40, 0x0, 0x0, 0x0,

    /* U+0070 "p" */
    0xbf, 0xf9, 0x0, 0x38, 0xce, 0xfe, 0xc8, 0x30,
    0x0, 0x0, 0xb, 0xff, 0x91, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0xbf, 0xfb, 0xdf,
    0xff, 0xfd, 0xdf, 0xff, 0xff, 0xe3, 0x0, 0xb,
    0xff, 0xff, 0xfd, 0x50, 0x0, 0x4, 0xbf, 0xff,
    0xe1, 0x0, 0xbf, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xb0, 0xb, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x40, 0xbf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf9, 0xb, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xd0, 0xbf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xb, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf0, 0xbf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xb, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xd0, 0xbf, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf9,
    0xb, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x40, 0xbf, 0xff, 0xfc, 0x20, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xb0, 0xb, 0xff, 0xff,
    0xff, 0x83, 0x0, 0x27, 0xdf, 0xff, 0xe1, 0x0,
    0xbf, 0xfc, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0xb, 0xff, 0xb0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x28, 0xce, 0xfe, 0xc8, 0x30, 0x0, 0x0, 0xb,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfd, 0xb6, 0x0,
    0xf, 0xff, 0x40, 0x0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xfe, 0x40, 0xff, 0xf4, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0x40,
    0x7, 0xff, 0xff, 0xa4, 0x10, 0x15, 0xbf, 0xff,
    0xff, 0xf4, 0x3, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0x40, 0xbf, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x1f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x44, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf4, 0x6f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x47, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf4, 0x6f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x44, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf4, 0x1f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0x40, 0xbf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf4, 0x3, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x40, 0x7, 0xff,
    0xff, 0xa4, 0x10, 0x15, 0xbf, 0xff, 0xff, 0xf4,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0x40, 0x0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xfd, 0x33, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x6a, 0xdf, 0xfd, 0xb5, 0x0, 0x3f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf4,

    /* U+0072 "r" */
    0xbf, 0xf9, 0x0, 0x38, 0xce, 0x8b, 0xff, 0x90,
    0xaf, 0xff, 0xf8, 0xbf, 0xf9, 0xbf, 0xff, 0xff,
    0x8b, 0xff, 0xff, 0xff, 0x96, 0x42, 0xbf, 0xff,
    0xfc, 0x10, 0x0, 0xb, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x50, 0x0, 0x0, 0xb, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xbf,
    0xfb, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x6, 0xad, 0xff, 0xed, 0xa7, 0x20,
    0x0, 0x0, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x1f, 0xff, 0xe6, 0x10, 0x0, 0x25,
    0xbf, 0x60, 0x6, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x8f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf8, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xfd, 0xa7, 0x30, 0x0, 0x0, 0x0, 0x6e, 0xff,
    0xff, 0xff, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x4,
    0x8b, 0xef, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x14, 0x8e, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xd0,
    0x7f, 0xfc, 0x73, 0x0, 0x0, 0x3a, 0xff, 0xf8,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0x6a, 0xde, 0xff, 0xeb, 0x82,
    0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x5c, 0xcd, 0xff, 0xfd, 0xcc, 0xcc,
    0x80, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x50, 0x1, 0x81, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xef, 0xf7, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x4a, 0xef, 0xfc,
    0x81,

    /* U+0075 "u" */
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x1e, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf1, 0xef, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x1e, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf1, 0xef, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x1e,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf1, 0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x1e, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf1, 0xef, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x1e, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf1, 0xef,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x1d, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf1, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x19, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf1, 0x4f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0x10, 0xcf,
    0xff, 0xb3, 0x0, 0x1, 0x6e, 0xff, 0xff, 0xf1,
    0x2, 0xef, 0xff, 0xff, 0xde, 0xff, 0xff, 0xaf,
    0xff, 0x10, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xfe,
    0x53, 0xff, 0xf1, 0x0, 0x0, 0x4a, 0xdf, 0xfe,
    0xb6, 0x0, 0x3f, 0xff, 0x10,

    /* U+0076 "v" */
    0xd, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xe0, 0x6, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x70, 0x0, 0xef,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x10, 0x0, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf9, 0x0, 0x0, 0x1f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf2, 0x0, 0x0,
    0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x20, 0x0, 0x0, 0xef,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x90,
    0x0, 0x6, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf1, 0x0, 0xd, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xf7, 0x0, 0x4f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xfe, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x52, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xc9,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0,

    /* U+0077 "w" */
    0x9f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfe,
    0x3, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x90, 0xd, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf3, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfd, 0x0, 0x1, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xef, 0xf6, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x70, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x4, 0xff, 0xd7, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xff, 0xf1, 0x0, 0x0, 0x5f, 0xfd, 0x0, 0x0,
    0x0, 0xaf, 0xf7, 0x1f, 0xff, 0x20, 0x0, 0x0,
    0x6f, 0xfb, 0x0, 0x0, 0x0, 0xff, 0xf3, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0xbf, 0xf7, 0x0, 0x0,
    0xc, 0xff, 0x50, 0x0, 0x0, 0x9, 0xff, 0x90,
    0x0, 0x7, 0xff, 0xb0, 0x4, 0xff, 0xd0, 0x0,
    0x2, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x3f, 0xfe,
    0x0, 0x0, 0xdf, 0xf4, 0x0, 0xe, 0xff, 0x30,
    0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf5, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xb0, 0x9, 0xff, 0x80, 0x0, 0x2, 0xff,
    0xf0, 0x4, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x10, 0xef, 0xf2, 0x0, 0x0, 0xc,
    0xff, 0x50, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf6, 0x5f, 0xfc, 0x0, 0x0, 0x0,
    0x6f, 0xfb, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xcb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0,

    /* U+0078 "x" */
    0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x90, 0x1, 0xef, 0xfd, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xfc, 0x0, 0x0, 0x3f, 0xff, 0xa0,
    0x0, 0x0, 0xb, 0xff, 0xe1, 0x0, 0x0, 0x7,
    0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x30, 0x4, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xd1, 0x1e,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xfb, 0xcf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf5, 0x7f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x90, 0xb,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x1, 0xef, 0xfc,
    0x0, 0x1, 0xef, 0xfc, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xe1, 0x0, 0x0, 0x3f, 0xff, 0xa0, 0x0,
    0x0, 0x9f, 0xff, 0x40, 0x0, 0x0, 0x7, 0xff,
    0xf6, 0x0, 0x5, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x30, 0x2f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xe1,

    /* U+0079 "y" */
    0xd, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xe0, 0x6, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x70, 0x0, 0xef,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x10, 0x0, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf9, 0x0, 0x0, 0x1f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf2, 0x0, 0x0,
    0x9, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x40, 0x0, 0x0, 0x0, 0xbf,
    0xfd, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xb0,
    0x0, 0x5, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf2, 0x0, 0xc, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf9, 0x0, 0x3f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x10, 0xaf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x72, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe9,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x1, 0xef, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xa3, 0x0, 0x3d, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x9d, 0xff, 0xd9, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x25, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x4c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0x59, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60,

    /* U+007B "{" */
    0x0, 0x0, 0x7, 0xcf, 0xff, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0x0, 0x8, 0xff, 0xff, 0xec, 0x0,
    0xd, 0xff, 0xf3, 0x0, 0x0, 0xf, 0xff, 0x80,
    0x0, 0x0, 0xf, 0xff, 0x60, 0x0, 0x0, 0x1f,
    0xff, 0x60, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0,
    0x0, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x1f, 0xff,
    0x60, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0, 0x0,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x1f, 0xff, 0x60,
    0x0, 0x0, 0x1f, 0xff, 0x50, 0x0, 0x0, 0x8f,
    0xff, 0x30, 0x0, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0xcd, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x40, 0x0, 0x0,
    0x1f, 0xff, 0x50, 0x0, 0x0, 0x1f, 0xff, 0x60,
    0x0, 0x0, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x1f,
    0xff, 0x60, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0,
    0x0, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x1f, 0xff,
    0x60, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0, 0x0,
    0xf, 0xff, 0x60, 0x0, 0x0, 0xf, 0xff, 0x90,
    0x0, 0x0, 0xd, 0xff, 0xf3, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xec, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x0, 0x0, 0x7, 0xcf, 0xff,

    /* U+007C "|" */
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff, 0x3f, 0xff,
    0x3f, 0xff,

    /* U+007D "}" */
    0x5f, 0xfe, 0xb4, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xef, 0xfa, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x30, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x80, 0x0, 0x3,
    0xff, 0xff, 0xd7, 0x0, 0x0, 0x9f, 0xff, 0x10,
    0x0, 0x0, 0xb, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xbf, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0xef, 0xfa, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x80, 0x0, 0x4d, 0xff, 0xff,
    0xf2, 0x0, 0x5, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x5f, 0xfe, 0xa4, 0x0, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x6c, 0xfe, 0xb3, 0x0, 0x0, 0x0, 0x7f,
    0xd0, 0x8f, 0xff, 0xff, 0xf9, 0x0, 0x0, 0xb,
    0xfb, 0x3f, 0xff, 0xbb, 0xff, 0xfc, 0x20, 0x4,
    0xff, 0x79, 0xff, 0x30, 0x3, 0xdf, 0xff, 0xbb,
    0xff, 0xf2, 0xcf, 0x90, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf7, 0xa, 0xc5, 0x0, 0x0, 0x0, 0x4b,
    0xff, 0xc5, 0x0,

    /* U+00B0 "°" */
    0x0, 0x1, 0x7b, 0xdb, 0x71, 0x0, 0x0, 0x4,
    0xef, 0xff, 0xff, 0xf5, 0x0, 0x2, 0xff, 0x92,
    0x2, 0x8f, 0xf3, 0x0, 0xcf, 0x70, 0x0, 0x0,
    0x7f, 0xd0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0xef,
    0x34, 0xfa, 0x0, 0x0, 0x0, 0xa, 0xf6, 0x5f,
    0xa0, 0x0, 0x0, 0x0, 0xaf, 0x62, 0xfe, 0x0,
    0x0, 0x0, 0xd, 0xf4, 0xd, 0xf6, 0x0, 0x0,
    0x6, 0xfe, 0x0, 0x4f, 0xf7, 0x10, 0x7, 0xff,
    0x50, 0x0, 0x6f, 0xff, 0xef, 0xff, 0x70, 0x0,
    0x0, 0x29, 0xdf, 0xea, 0x30, 0x0,

    /* U+2022 "•" */
    0x0, 0x37, 0x50, 0x0, 0x9f, 0xff, 0xd1, 0x5f,
    0xff, 0xff, 0x99, 0xff, 0xff, 0xfd, 0x7f, 0xff,
    0xff, 0xb1, 0xff, 0xff, 0xf5, 0x3, 0xcf, 0xd5,
    0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6a, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0xcf, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xae, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcc, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xea, 0x51, 0x8, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x83, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb6, 0x20, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xe9, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xfd, 0x72, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x69, 0xbc, 0xac, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x3, 0x43, 0x1f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x80, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xdf, 0xff, 0xff, 0xa3, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x23, 0x20,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf,
    0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7a, 0xba, 0x85, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0x6f, 0x40, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xf6, 0xff, 0x40, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x4, 0xff, 0xff, 0xec, 0xcc, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xcc, 0xce, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa6, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x69, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc8, 0x88, 0xcf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xfc, 0x88,
    0x8c, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0, 0x3f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf4, 0x0, 0x4, 0xff, 0xff, 0x40,
    0x0, 0x3f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x4, 0xff,
    0xff, 0x60, 0x0, 0x6f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf7, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x94,
    0x44, 0x9f, 0xff, 0x73, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x37, 0xff, 0xf9, 0x44, 0x49, 0xff,
    0xff, 0x40, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0x40, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x4, 0xff, 0xff, 0x94,
    0x44, 0x9f, 0xff, 0x73, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x37, 0xff, 0xf9, 0x44, 0x49, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x6f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf7, 0x0, 0x6, 0xff, 0xff, 0x40,
    0x0, 0x3f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0x0, 0x4, 0xff,
    0xff, 0x40, 0x0, 0x3f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf4, 0x0,
    0x4, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xc8, 0x88, 0xcf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xfc, 0x88, 0x8c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa6, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x69, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xec, 0xcc, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xcc,
    0xce, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x4, 0xff, 0x7f, 0x40, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x4, 0xf6,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6e, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x9, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xfc, 0x10, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0xfc, 0x10, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xff,
    0xff, 0xff, 0xfb, 0x10, 0x9, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0xa, 0xe7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x80, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8d, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x6c, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xcc, 0x40, 0x0, 0x8f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x50, 0x7f, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0x3e, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xfa, 0xef, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xa4, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x5, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xe7, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0xef, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xae, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xfa, 0x7f, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0x30,
    0x8f, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0x50, 0x0, 0x6c, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xcc, 0x40,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x56, 0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xed, 0x10, 0x0, 0xe,
    0xff, 0xff, 0xb0, 0x0, 0x4, 0xed, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xb0,
    0x0, 0xe, 0xff, 0xff, 0xb0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xf4, 0x0, 0xe, 0xff, 0xff, 0xb0, 0x0,
    0x7f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xf9, 0x0, 0xe, 0xff, 0xff,
    0xb0, 0x0, 0xdf, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xe,
    0xff, 0xff, 0xb0, 0x0, 0x9f, 0xff, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0x70,
    0x0, 0xe, 0xff, 0xff, 0xb0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0xe, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xef,
    0xff, 0xff, 0x70, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfb, 0x0,
    0x5, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0x20, 0xa, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x70, 0xe, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xb0, 0x2f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xe0,
    0x4f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xf1, 0x5f, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf2, 0x6f, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf3, 0x6f, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf2,
    0x5f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x4,
    0xcd, 0xdb, 0x20, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xf1, 0x2f, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xc0, 0xb, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x80,
    0x6, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x30, 0x0, 0xef, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf5, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xf9, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf8, 0x30, 0x0, 0x0, 0x4, 0xaf, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xdb, 0xbd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x15, 0x9c, 0xde, 0xed,
    0xb9, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4a, 0xce, 0xee, 0xda, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0x30, 0x0, 0x7, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x20, 0x0, 0x18,
    0x30, 0x0, 0x0, 0x0, 0x9, 0xff, 0xa2, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x6e, 0xff, 0x20, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x60, 0x0, 0x2a, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x3, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x6e, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x3, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x60, 0x0, 0x2a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x9, 0xff, 0xb2,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x6e, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7,
    0x40, 0x0, 0x6, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x20, 0x0, 0x7, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4a, 0xce, 0xee, 0xda, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x62, 0x0, 0x0, 0x0, 0x6, 0x77,
    0x76, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xf8,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x5f,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xfd, 0x20, 0x5, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x5f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xff,
    0xec, 0xff, 0xff, 0xff, 0x75, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xc1, 0x6, 0xff, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0x70, 0x2, 0x50, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff,
    0x40, 0x5, 0xff, 0xb0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xfd, 0x20, 0x7, 0xff, 0xff,
    0xd2, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xfb,
    0x10, 0xa, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x5f,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xf9, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x3e, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf6,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x1c, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xe3, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0xa, 0xff,
    0xff, 0xff, 0x70, 0x4, 0xef, 0xff, 0xff, 0xd2,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x20, 0x7, 0xff, 0xff, 0xff, 0xa0,
    0xef, 0xff, 0xff, 0xb0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x4, 0xff, 0xff, 0xff, 0x67, 0xff, 0xff, 0x80,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x2, 0xdf, 0xff,
    0xe1, 0xa, 0xff, 0x50, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x1, 0xbf, 0xf3, 0x0, 0x9, 0x30,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x75, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0xbb, 0xbb, 0xb7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x33, 0x33,
    0x39, 0xff, 0xff, 0xff, 0xff, 0x93, 0x33, 0x33,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x8, 0xff, 0xff, 0x80, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x8f, 0xf8, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x5, 0x50,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x61, 0x16, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xcf, 0xfd, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xd, 0xf2, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xc,
    0xf0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x8f, 0xfa, 0x5d, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa3,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0x0, 0x19, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xf9,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x8d, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xc0, 0x0, 0x1, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0x70, 0x0, 0xaf, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x20, 0x5f, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xfc, 0xc, 0xff, 0xff, 0xc8, 0x88, 0x88,
    0x88, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x88, 0x88, 0x88, 0x88, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x55, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x5, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xa1, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xbb, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0x9b, 0xde, 0xec, 0xa7, 0x30, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x71, 0x0, 0x0, 0xe, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xe, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0xd,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xc, 0xff, 0xff, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xb6, 0x42, 0x24, 0x7c, 0xff,
    0xff, 0xff, 0xff, 0x9b, 0xff, 0xff, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0x81, 0x0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x1e, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0x1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8e, 0xed, 0xcb, 0xaa, 0xff, 0xff, 0xff, 0xff,
    0x6, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xe0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xe0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xa0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x60, 0xff, 0xff, 0xff, 0xff, 0xaa, 0xbc,
    0xdd, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfe, 0x0, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf7, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xe1, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x81, 0x0, 0x0, 0x0, 0x0, 0x18, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xff, 0xff, 0xca, 0xff,
    0xff, 0xff, 0xff, 0xb7, 0x42, 0x24, 0x6b, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0xff, 0xff,
    0xc0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xd0, 0x5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xe0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x17, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x3, 0x7a, 0xde, 0xed,
    0xb9, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8b, 0xbb, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0x16,
    0x77, 0x77, 0x77, 0x7e, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8b, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x4e, 0xe4, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xf5, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x6f,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x5f, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x9f, 0xfd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x3f, 0xff, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x5f, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xa, 0xff, 0xf7,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x5f, 0xf7, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x11, 0x0, 0x1, 0x67, 0x77, 0x77, 0x77,
    0xef, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x51,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0x0, 0x0, 0x0, 0x2,
    0xa8, 0x0, 0x0, 0x3f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xbf, 0xfd, 0x30, 0x0,
    0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0x30, 0x0, 0x9f, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xfe,
    0x20, 0x1, 0xef, 0xfc, 0x0, 0x16, 0x77, 0x77,
    0x77, 0x7e, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfb, 0x0, 0x7, 0xff,
    0xf3, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xf5, 0x0, 0x1f, 0xff, 0x80, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x4, 0xff, 0x50, 0x0, 0x1e, 0xff, 0xc0, 0x0,
    0xbf, 0xfd, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xaf, 0xff, 0x60,
    0x0, 0x8f, 0xff, 0x20, 0x6, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x5, 0xff, 0xff, 0x20, 0x1, 0xff, 0xf6,
    0x0, 0x2f, 0xff, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x4, 0xff,
    0xf9, 0x0, 0xd, 0xff, 0x90, 0x0, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x9, 0xff, 0xd0, 0x0, 0xbf,
    0xfb, 0x0, 0xf, 0xff, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x6f, 0xff, 0x0, 0xa, 0xff, 0xc0, 0x0, 0xef,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x9, 0xff, 0xd0, 0x0,
    0xbf, 0xfb, 0x0, 0xf, 0xff, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x4, 0xff, 0xf9, 0x0, 0xd, 0xff, 0x90, 0x0,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x5, 0xff, 0xff, 0x20,
    0x1, 0xff, 0xf6, 0x0, 0x3f, 0xff, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xaf, 0xff, 0x60, 0x0, 0x8f, 0xff, 0x20,
    0x7, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x1e, 0xff, 0xc0, 0x0, 0xbf, 0xfd, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf5,
    0x0, 0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xfb, 0x0, 0x8, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xfe,
    0x20, 0x1, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x30, 0x0, 0xaf, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xbf, 0xfd,
    0x30, 0x0, 0x5f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x2, 0xa8, 0x0, 0x0, 0x3f, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xb4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x50, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x30, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x1e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x4a, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x73, 0x48, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x1c, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x1, 0xcf, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x1c, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x1, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x8c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x5, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x50,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3b, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xfc, 0x29, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0x80, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbd, 0xff, 0xf9, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xcf, 0xff, 0xd0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x87, 0xff, 0xff, 0x40, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x2f, 0xff,
    0xfc, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0xbf, 0xff, 0xfa, 0x0, 0x18,
    0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x3,
    0xff, 0xff, 0xfb, 0x10, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0x83, 0x10, 0xbf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0xff, 0xff, 0xff, 0xfc, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x56,
    0x76, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F048 "" */
    0x6, 0x77, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x65, 0x0, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xc0,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xf2, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfd, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf3, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf3,
    0x4f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0x2f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x57, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x57, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x47, 0x77, 0x77, 0x77, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x47, 0x77, 0x77, 0x77, 0x62, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0x0, 0x6, 0xef, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x0, 0x47, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x62, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x1, 0x65, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x57, 0x77, 0x42, 0xef, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x7f,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x1, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x1f, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x1, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa1, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x5f, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x31, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x1f, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x1, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x1f, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0x5f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xf0, 0xaf, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x6d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe7, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x3,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xc5, 0x0,

    /* U+F054 "" */
    0x0, 0x9, 0xb3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xc4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x55,
    0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x19, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xef, 0xff,
    0xff, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x19, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcb, 0x50,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x70,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x37,
    0xad, 0xef, 0xfe, 0xdb, 0x85, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x76,
    0x8a, 0xef, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff,
    0xff, 0xf8, 0x10, 0x0, 0x0, 0x0, 0x5e, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x14, 0x53, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x3, 0xff, 0xff, 0x80, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xc1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0x50, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x3, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x11, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x90, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x6, 0xfc, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x23, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xa, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0x90, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x8e, 0xff,
    0xfb, 0x40, 0x0, 0xb, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff,
    0xff, 0xff, 0xf8, 0x10, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xc8, 0x66, 0x7a, 0xef, 0xff, 0xff, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x37, 0xad, 0xef, 0xfe,
    0xdb, 0x85, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x16, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x3, 0x7b,
    0xdf, 0xfe, 0xdc, 0x96, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xf9, 0x0, 0x16, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd7, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xfc, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x81, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x76,
    0x79, 0xdf, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x50, 0x0, 0x0,
    0x0, 0x2b, 0xff, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x2, 0x32, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xf6, 0x1, 0xff, 0xfe, 0x80,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xfa, 0xb, 0xff, 0xff, 0xd2,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xe4, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff, 0xd0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xfe, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xff, 0x30, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0x10, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xfd, 0x30, 0xef, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0xff, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xef, 0xff, 0xff, 0xff, 0xd9, 0x66, 0x76, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x26, 0x9c, 0xde, 0xfe, 0xdc, 0x95, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xdf, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x16, 0x10, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x56, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x75,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x20, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x31, 0x5e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x17, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x84,
    0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xe2, 0x0, 0x7a, 0xaa, 0xaa, 0xaa, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8a, 0xaa, 0xaf,
    0xff, 0xff, 0xfe, 0x20, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf9, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xb0, 0x4,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0xf, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfc,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0x50, 0x0, 0xf,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xa0, 0x3, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0xc, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x1, 0x63, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1,
    0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xfa, 0x0, 0x56, 0x0,
    0x0, 0xc, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xb0, 0x4,
    0xff, 0x50, 0x0, 0xf, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfb,
    0x0, 0x3f, 0xff, 0xf4, 0x0, 0xf, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xc0, 0x3, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xf5, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x7a, 0xaa,
    0xaa, 0xaa, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8a, 0xaa, 0xaf, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xa0,
    0x3f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xa0,
    0x8f, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf0,
    0x3f, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xb0,
    0x5, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfc, 0x0,
    0x0, 0x4c, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0x90, 0x0,

    /* U+F078 "" */
    0x0, 0x4c, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x90, 0x0,
    0x5, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfc, 0x0,
    0x3f, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xb0,
    0x8f, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xf0,
    0x3f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xa0,
    0x4, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0,
    0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x2, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x6e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0x7c, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xf3, 0x1f, 0xff, 0xf6, 0xc,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x8f, 0xf4, 0x1, 0xff, 0xff, 0x60, 0x1d, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x1f, 0xff, 0xf6, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xa9, 0x10, 0x1f,
    0xff, 0xf6, 0x0, 0x6b, 0x60, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xfc, 0x1, 0xff, 0xff,
    0x60, 0x7f, 0xff, 0x60, 0x0, 0x0, 0x1, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xfb, 0x1f, 0xff, 0xf6, 0x6f,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xbf, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xb8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x40, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x75, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x5, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x50,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9c, 0xcc, 0xcc,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xcc, 0xcc,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x40, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x8, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x68, 0x88, 0x88,
    0x73, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x10, 0x0, 0x0, 0x1, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xcf, 0xfd, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xd, 0xf2, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xc,
    0xf0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x8f, 0xfa, 0x5d, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x3a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa3,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xfe, 0xb7, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x84, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x2, 0xdf, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x4d, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe9, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x77, 0x65, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x3, 0x54, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xfc, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xfb, 0x20,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xe3,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xf5,
    0xbf, 0xff, 0xf9, 0x35, 0xef, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0x70,
    0xef, 0xff, 0xb0, 0x0, 0x3f, 0xff, 0xf6, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0x80, 0x0, 0xf, 0xff, 0xf7, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0xef, 0xff, 0xd0, 0x0, 0x5f, 0xff, 0xf6, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xaf, 0xff, 0xfc, 0x68, 0xff, 0xff, 0xf2, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x3,
    0xef, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x55, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7d,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xf9, 0x35, 0xef, 0xff, 0xf2, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0xef, 0xff, 0xb0, 0x0, 0x3f, 0xff, 0xf6, 0x0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0xff, 0xff, 0x80, 0x0, 0xf, 0xff, 0xf7, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xef, 0xff, 0xd0, 0x0, 0x5f, 0xff, 0xf6, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xaf, 0xff, 0xfc, 0x68, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf6,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xd7, 0x0,
    0x0, 0x4, 0xcf, 0xff, 0xe8, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xab, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x50, 0xa, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0xfa,
    0x6e, 0xff, 0xff, 0xb0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x44, 0x44, 0x44, 0x43,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0xff, 0xff, 0xff, 0xf1, 0x6, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x47, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0xff, 0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0x60, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf5, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0x50,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xe0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xe9, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x89, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x10,
    0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb1, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x97,
    0x8d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C9 "" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10,

    /* U+F0E0 "" */
    0x5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x50, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x1b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x30, 0x1, 0xf7, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb1, 0x0, 0x7f, 0xff, 0xb1, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x1b, 0xff, 0xff, 0xfe,
    0x40, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x30, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x50, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x5e,
    0xff, 0xff, 0xe5, 0x0, 0x2d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x1, 0x8e, 0xe8, 0x10, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x6e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x5, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x50,

    /* U+F0E7 "" */
    0x0, 0x0, 0x34, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5a, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x9a, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x66, 0x66, 0x6a, 0xff, 0xff, 0xff, 0xd6,
    0x66, 0x66, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0x53, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x53, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb, 0xbb,
    0xbb, 0xbb, 0xbb, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1f, 0x80, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1f, 0xf9, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1f, 0xff, 0x90, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1f, 0xff, 0xf9, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1f, 0xff, 0xff, 0x90,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x1f, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0xb, 0xbb, 0xbb, 0xb7,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x6e, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xab, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x91,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xff,
    0xff, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x47,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x5, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xa1, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xe8, 0x88, 0xaf, 0xfc,
    0x88, 0x8c, 0xff, 0xa8, 0x88, 0xdf, 0xf9, 0x88,
    0x9f, 0xfd, 0x88, 0x8a, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0xff, 0x40, 0x0, 0x4f, 0xf0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0xff, 0x50, 0x0,
    0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0x0, 0xf,
    0xf4, 0x0, 0x4, 0xff, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0xff, 0xff, 0x8f,
    0xff, 0xf8, 0x0, 0x0, 0xff, 0x40, 0x0, 0x4f,
    0xf0, 0x0, 0x5, 0xfe, 0x0, 0x0, 0xef, 0x50,
    0x0, 0xf, 0xff, 0xf8, 0xff, 0xff, 0xa0, 0x0,
    0x2f, 0xf6, 0x0, 0x6, 0xff, 0x20, 0x0, 0x8f,
    0xf1, 0x0, 0x1f, 0xf8, 0x0, 0x2, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xf5, 0x44, 0x5f,
    0xfb, 0x44, 0x46, 0xff, 0xa4, 0x44, 0x7f, 0xf7,
    0x44, 0x4b, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0xdf, 0x60, 0x0, 0xe,
    0xf5, 0x0, 0x0, 0xff, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0xd, 0xf6, 0x0, 0x0, 0xef, 0x50, 0x0, 0xf,
    0xf0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0xdf, 0x60, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0xff, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xf5,
    0x44, 0x5f, 0xfb, 0x44, 0x46, 0xff, 0xa4, 0x44,
    0x7f, 0xf7, 0x44, 0x4b, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xa0, 0x0, 0x2f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf8, 0x0, 0x2, 0xff, 0xff, 0x8f, 0xff, 0xf8,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x50, 0x0, 0xf,
    0xff, 0xf8, 0xff, 0xff, 0x80, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf5, 0x0, 0x0, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x50, 0x0,
    0xf, 0xff, 0xf8, 0xff, 0xff, 0xe8, 0x88, 0xaf,
    0xfc, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x9f, 0xfd, 0x88, 0x8a, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x55, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x5, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xa1, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x47, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7e,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x18, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x29, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3a, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x0, 0x30, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xf, 0xd1,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0xff, 0xd1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0xf, 0xff, 0xd1, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xff,
    0xff, 0xd1, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xd1,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xff, 0xff, 0xff, 0xd1, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xf,
    0xff, 0xff, 0xff, 0xd1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0xb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x3a, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xba, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x34, 0x32, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x47, 0xad, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xa7, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0x87,
    0x66, 0x67, 0x8a, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x30, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xaf, 0xff, 0xff, 0xff, 0xfa, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xff, 0xff, 0xff, 0xff, 0xae,
    0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xfe, 0x3f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0x30, 0x3f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x6a, 0xce, 0xff,
    0xfe, 0xca, 0x62, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x30, 0x0, 0x3b, 0x40, 0x0, 0x0,
    0x0, 0x1, 0x8d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x81, 0x0, 0x0, 0x0, 0x0, 0x3b,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x19,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xcb,
    0xab, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfc, 0x62, 0x0, 0x0, 0x0,
    0x2, 0x6c, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xcf, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4b, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3b,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b, 0xdb,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x40, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x5, 0x7b, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xc8,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xef, 0xff, 0xd6, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0, 0x0,

    /* U+F241 "" */
    0x0, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x40, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x5, 0x7b, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xc8,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xef, 0xff, 0xd6, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0, 0x0,

    /* U+F242 "" */
    0x0, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x40, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x7b, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xc8,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xef, 0xff, 0xd6, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0, 0x0,

    /* U+F243 "" */
    0x0, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x40, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x7b, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x1f, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x1, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x1f, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xc8,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xef, 0xff, 0xd6, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0, 0x0,

    /* U+F244 "" */
    0x0, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x40, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x7b, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xc8,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0xef, 0xff, 0xd6, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xb2, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x52, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xdf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2a, 0xdd,
    0xdf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xbb, 0xbf, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf4, 0x0, 0xb, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xfe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x44, 0x10, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x91, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x1, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x60, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0xa, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfc, 0x30, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xfe,
    0x88, 0xbf, 0xff, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x9c, 0xff, 0xff,
    0xf9, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0xaf, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x91, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xc3, 0x0, 0x0, 0x3, 0xef,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x6, 0xaa,
    0x61, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x20, 0x7,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xe8, 0x8b, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xbe, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x77, 0x77, 0x77, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x23, 0x32,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6a, 0xef, 0xff, 0xff, 0xfe, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xe1, 0xdf,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x2e, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0x0, 0xd, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xe0, 0x9, 0x30, 0x8, 0xff, 0xff,
    0xff, 0x40, 0x1f, 0xff, 0xff, 0xb0, 0x5f, 0xff,
    0xe0, 0xa, 0xf3, 0x0, 0x9f, 0xff, 0xff, 0x70,
    0x3f, 0xff, 0xff, 0x10, 0x5, 0xff, 0xe0, 0xa,
    0xff, 0x20, 0xb, 0xff, 0xff, 0xa0, 0x6f, 0xff,
    0xff, 0xd1, 0x0, 0x5f, 0xe0, 0x9, 0xfa, 0x0,
    0x3f, 0xff, 0xff, 0xc0, 0x8f, 0xff, 0xff, 0xfd,
    0x10, 0x4, 0xe0, 0x9, 0xa0, 0x2, 0xef, 0xff,
    0xff, 0xe0, 0x9f, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x30, 0x4, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xaf, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf0, 0x9f, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x40, 0x5, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xfc, 0x0, 0x5,
    0xe0, 0x9, 0xb0, 0x0, 0xbf, 0xff, 0xff, 0xd0,
    0x6f, 0xff, 0xff, 0xc0, 0x0, 0x6f, 0xf0, 0x9,
    0xfa, 0x0, 0xc, 0xff, 0xff, 0xc0, 0x3f, 0xff,
    0xff, 0x10, 0x6, 0xff, 0xf0, 0xa, 0xfe, 0x10,
    0x8, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xc1,
    0x6f, 0xff, 0xf0, 0xa, 0xe2, 0x0, 0x7f, 0xff,
    0xff, 0x70, 0xc, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xf0, 0x8, 0x20, 0x7, 0xff, 0xff, 0xff, 0x40,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x68, 0x9a, 0xba,
    0x97, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xbb, 0xbb,
    0xbb, 0xbb, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x37, 0x77, 0x77, 0x77, 0x79, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x77, 0x77, 0x77, 0x77, 0x60,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x82, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0x22, 0xff, 0xff, 0xd1,
    0x6f, 0xff, 0xfa, 0xa, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xfe, 0x0, 0xef, 0xff, 0xa0,
    0x2f, 0xff, 0xf6, 0x6, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0x22, 0xff, 0xff, 0xd1,
    0x6f, 0xff, 0xfa, 0xa, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0,
    0x0, 0x0, 0x47, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x87, 0x61, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xb1, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xd1,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xd1, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xd1, 0x1, 0xdf,
    0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xd1, 0x1, 0xdf, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0x1, 0xdf, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x1,
    0xde, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xfd, 0xca, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x76, 0x53, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbd, 0xff, 0xff, 0xff, 0xff,
    0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xc, 0xff, 0xff, 0xff, 0xc0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0xc, 0xff, 0xff, 0xc0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xc,
    0xc0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0xc, 0xc0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xc,
    0xff, 0xff, 0xc0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0xc, 0xff, 0xff,
    0xff, 0xc0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbd, 0xff, 0xff, 0xff, 0xff,
    0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80, 0x0,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x28, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x75, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x1d, 0xff, 0xe0,
    0x0, 0xef, 0x50, 0x2, 0xff, 0x20, 0x8, 0xff,
    0xff, 0x0, 0x1d, 0xff, 0xfe, 0x0, 0xe, 0xf5,
    0x0, 0x2f, 0xf2, 0x0, 0x8f, 0xff, 0xf0, 0x1d,
    0xff, 0xff, 0xe0, 0x0, 0xef, 0x50, 0x2, 0xff,
    0x20, 0x8, 0xff, 0xff, 0x1d, 0xff, 0xff, 0xfe,
    0x0, 0xe, 0xf5, 0x0, 0x2f, 0xf2, 0x0, 0x8f,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xef,
    0x50, 0x2, 0xff, 0x20, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0xe, 0xf5, 0x0, 0x2f,
    0xf2, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0xef, 0x50, 0x2, 0xff, 0x20, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x39,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xb9, 0x30, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x37, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xa, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x10, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf1, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x10, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x11, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x1, 0xcf, 0xff, 0xff, 0xff, 0xfc, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xa3, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 155, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 154, .box_w = 6, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 75, .adv_w = 225, .box_w = 10, .box_h = 10, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 125, .adv_w = 405, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 438, .adv_w = 358, .box_w = 20, .box_h = 34, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 778, .adv_w = 486, .box_w = 29, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1141, .adv_w = 395, .box_w = 24, .box_h = 26, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1453, .adv_w = 121, .box_w = 4, .box_h = 10, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 1473, .adv_w = 194, .box_w = 8, .box_h = 33, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 1605, .adv_w = 195, .box_w = 8, .box_h = 33, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1737, .adv_w = 230, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 12},
    {.bitmap_index = 1835, .adv_w = 335, .box_w = 17, .box_h = 16, .ofs_x = 2, .ofs_y = 5},
    {.bitmap_index = 1971, .adv_w = 131, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 2001, .adv_w = 221, .box_w = 10, .box_h = 3, .ofs_x = 2, .ofs_y = 8},
    {.bitmap_index = 2016, .adv_w = 131, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2031, .adv_w = 203, .box_w = 16, .box_h = 34, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 2303, .adv_w = 384, .box_w = 22, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2578, .adv_w = 213, .box_w = 10, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2703, .adv_w = 331, .box_w = 20, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2953, .adv_w = 329, .box_w = 19, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3191, .adv_w = 385, .box_w = 23, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3479, .adv_w = 331, .box_w = 20, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3729, .adv_w = 355, .box_w = 21, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3992, .adv_w = 344, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4242, .adv_w = 371, .box_w = 21, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4505, .adv_w = 355, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4755, .adv_w = 131, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4812, .adv_w = 131, .box_w = 6, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 4884, .adv_w = 335, .box_w = 17, .box_h = 17, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 5029, .adv_w = 335, .box_w = 17, .box_h = 11, .ofs_x = 2, .ofs_y = 7},
    {.bitmap_index = 5123, .adv_w = 335, .box_w = 17, .box_h = 17, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 5268, .adv_w = 330, .box_w = 19, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5506, .adv_w = 596, .box_w = 35, .box_h = 32, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 6066, .adv_w = 422, .box_w = 28, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6416, .adv_w = 436, .box_w = 23, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 6704, .adv_w = 416, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7004, .adv_w = 476, .box_w = 26, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7329, .adv_w = 386, .box_w = 20, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7579, .adv_w = 366, .box_w = 19, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 7817, .adv_w = 445, .box_w = 24, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8117, .adv_w = 468, .box_w = 23, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8405, .adv_w = 179, .box_w = 5, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8468, .adv_w = 295, .box_w = 16, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8668, .adv_w = 414, .box_w = 23, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 8956, .adv_w = 342, .box_w = 19, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 9194, .adv_w = 550, .box_w = 28, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 9544, .adv_w = 468, .box_w = 23, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 9832, .adv_w = 484, .box_w = 28, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10182, .adv_w = 416, .box_w = 22, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 10457, .adv_w = 484, .box_w = 29, .box_h = 30, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 10892, .adv_w = 419, .box_w = 22, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 11167, .adv_w = 358, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11417, .adv_w = 338, .box_w = 21, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11680, .adv_w = 456, .box_w = 22, .box_h = 25, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 11955, .adv_w = 410, .box_w = 27, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 12293, .adv_w = 649, .box_w = 39, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12781, .adv_w = 388, .box_w = 24, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13081, .adv_w = 373, .box_w = 25, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 13394, .adv_w = 378, .box_w = 22, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13669, .adv_w = 192, .box_w = 9, .box_h = 33, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 13818, .adv_w = 203, .box_w = 16, .box_h = 34, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 14090, .adv_w = 192, .box_w = 9, .box_h = 33, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 14239, .adv_w = 336, .box_w = 17, .box_h = 15, .ofs_x = 2, .ofs_y = 5},
    {.bitmap_index = 14367, .adv_w = 288, .box_w = 18, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14394, .adv_w = 346, .box_w = 10, .box_h = 5, .ofs_x = 4, .ofs_y = 22},
    {.bitmap_index = 14419, .adv_w = 344, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 14590, .adv_w = 393, .box_w = 21, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 14863, .adv_w = 329, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15044, .adv_w = 393, .box_w = 21, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15317, .adv_w = 353, .box_w = 20, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15507, .adv_w = 203, .box_w = 14, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15689, .adv_w = 397, .box_w = 21, .box_h = 26, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 15962, .adv_w = 392, .box_w = 19, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 16209, .adv_w = 161, .box_w = 6, .box_h = 27, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 16290, .adv_w = 164, .box_w = 12, .box_h = 34, .ofs_x = -4, .ofs_y = -7},
    {.bitmap_index = 16494, .adv_w = 355, .box_w = 20, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 16754, .adv_w = 161, .box_w = 4, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 16806, .adv_w = 609, .box_w = 32, .box_h = 19, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 17110, .adv_w = 392, .box_w = 19, .box_h = 19, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 17291, .adv_w = 366, .box_w = 21, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17491, .adv_w = 393, .box_w = 21, .box_h = 26, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 17764, .adv_w = 393, .box_w = 21, .box_h = 26, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 18037, .adv_w = 236, .box_w = 11, .box_h = 19, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 18142, .adv_w = 289, .box_w = 17, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18304, .adv_w = 238, .box_w = 14, .box_h = 23, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18465, .adv_w = 390, .box_w = 19, .box_h = 19, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 18646, .adv_w = 322, .box_w = 22, .box_h = 19, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 18855, .adv_w = 518, .box_w = 33, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19169, .adv_w = 318, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19359, .adv_w = 322, .box_w = 22, .box_h = 26, .ofs_x = -1, .ofs_y = -7},
    {.bitmap_index = 19645, .adv_w = 300, .box_w = 17, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 19807, .adv_w = 202, .box_w = 10, .box_h = 33, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 19972, .adv_w = 172, .box_w = 4, .box_h = 33, .ofs_x = 3, .ofs_y = -7},
    {.bitmap_index = 20038, .adv_w = 202, .box_w = 11, .box_h = 33, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 20220, .adv_w = 335, .box_w = 17, .box_h = 6, .ofs_x = 2, .ofs_y = 9},
    {.bitmap_index = 20271, .adv_w = 241, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 14},
    {.bitmap_index = 20349, .adv_w = 181, .box_w = 7, .box_h = 7, .ofs_x = 2, .ofs_y = 7},
    {.bitmap_index = 20374, .adv_w = 576, .box_w = 36, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 21040, .adv_w = 576, .box_w = 36, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 21526, .adv_w = 576, .box_w = 36, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22120, .adv_w = 576, .box_w = 36, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22606, .adv_w = 396, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 22919, .adv_w = 576, .box_w = 36, .box_h = 36, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 23567, .adv_w = 576, .box_w = 35, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 24215, .adv_w = 648, .box_w = 41, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24892, .adv_w = 576, .box_w = 36, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 25558, .adv_w = 648, .box_w = 41, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 26112, .adv_w = 576, .box_w = 36, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 26778, .adv_w = 288, .box_w = 18, .box_h = 29, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 27039, .adv_w = 432, .box_w = 27, .box_h = 29, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 27431, .adv_w = 648, .box_w = 41, .box_h = 35, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 28149, .adv_w = 576, .box_w = 36, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 28635, .adv_w = 396, .box_w = 25, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 29098, .adv_w = 504, .box_w = 24, .box_h = 33, .ofs_x = 4, .ofs_y = -3},
    {.bitmap_index = 29494, .adv_w = 504, .box_w = 32, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 30086, .adv_w = 504, .box_w = 32, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 30614, .adv_w = 504, .box_w = 32, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 31142, .adv_w = 504, .box_w = 23, .box_h = 33, .ofs_x = 4, .ofs_y = -3},
    {.bitmap_index = 31522, .adv_w = 504, .box_w = 34, .box_h = 33, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 32083, .adv_w = 360, .box_w = 20, .box_h = 31, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 32393, .adv_w = 360, .box_w = 20, .box_h = 31, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 32703, .adv_w = 504, .box_w = 32, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 33231, .adv_w = 504, .box_w = 32, .box_h = 7, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 33343, .adv_w = 648, .box_w = 41, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 33897, .adv_w = 720, .box_w = 47, .box_h = 37, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 34767, .adv_w = 648, .box_w = 42, .box_h = 37, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 35544, .adv_w = 576, .box_w = 36, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 36138, .adv_w = 504, .box_w = 32, .box_h = 19, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 36442, .adv_w = 504, .box_w = 32, .box_h = 19, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 36746, .adv_w = 720, .box_w = 45, .box_h = 29, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 37399, .adv_w = 576, .box_w = 36, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 37885, .adv_w = 576, .box_w = 36, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 38551, .adv_w = 576, .box_w = 37, .box_h = 37, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 39236, .adv_w = 504, .box_w = 32, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 39764, .adv_w = 504, .box_w = 32, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 40356, .adv_w = 504, .box_w = 32, .box_h = 33, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 40884, .adv_w = 504, .box_w = 32, .box_h = 29, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 41348, .adv_w = 576, .box_w = 36, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 41834, .adv_w = 360, .box_w = 24, .box_h = 37, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 42278, .adv_w = 504, .box_w = 32, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 42870, .adv_w = 504, .box_w = 32, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 43462, .adv_w = 648, .box_w = 41, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 44016, .adv_w = 576, .box_w = 38, .box_h = 38, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 44738, .adv_w = 432, .box_w = 27, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 45238, .adv_w = 720, .box_w = 45, .box_h = 34, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 46003, .adv_w = 720, .box_w = 45, .box_h = 23, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 46521, .adv_w = 720, .box_w = 45, .box_h = 23, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 47039, .adv_w = 720, .box_w = 45, .box_h = 23, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 47557, .adv_w = 720, .box_w = 45, .box_h = 23, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 48075, .adv_w = 720, .box_w = 45, .box_h = 23, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 48593, .adv_w = 720, .box_w = 46, .box_h = 29, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 49260, .adv_w = 504, .box_w = 28, .box_h = 37, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 49778, .adv_w = 504, .box_w = 32, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 50370, .adv_w = 576, .box_w = 37, .box_h = 37, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 51055, .adv_w = 720, .box_w = 45, .box_h = 27, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 51663, .adv_w = 432, .box_w = 27, .box_h = 37, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 52163, .adv_w = 579, .box_w = 37, .box_h = 23, .ofs_x = 0, .ofs_y = 2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 6, 0, 0, 0,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 26, 0, 16, -13, 0, 0,
    0, 0, -32, -35, 4, 27, 13, 10,
    -23, 4, 28, 2, 24, 6, 18, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 35, 5, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 0, -17, 0, 0, 0, 0,
    0, -12, 10, 12, 0, 0, -6, 0,
    -4, 6, 0, -6, 0, -6, -3, -12,
    0, 0, 0, 0, -6, 0, 0, -7,
    -9, 0, 0, -6, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -6, 0, -9, 0, -16, 0, -70, 0,
    0, -12, 0, 12, 17, 1, 0, -12,
    6, 6, 19, 12, -10, 12, 0, 0,
    -33, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -21, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -16, -7, -28, 0, -23,
    -4, 0, 0, 0, 0, 1, 22, 0,
    -17, -5, -2, 2, 0, -10, 0, 0,
    -4, -43, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -46, -5, 22,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -24, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 19,
    0, 6, 0, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 22, 5,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -21, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    12, 6, 17, -6, 0, 0, 12, -6,
    -19, -79, 4, 16, 12, 1, -7, 0,
    21, 0, 18, 0, 18, 0, -54, 0,
    -7, 17, 0, 19, -6, 12, 6, 0,
    0, 2, -6, 0, 0, -10, 46, 0,
    46, 0, 17, 0, 24, 7, 10, 17,
    0, 0, 0, -21, 0, 0, 0, 0,
    2, -4, 0, 4, -10, -7, -12, 4,
    0, -6, 0, 0, 0, -23, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -37, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, -32, 0, -36, 0, 0, 0,
    0, -4, 0, 57, -7, -7, 6, 6,
    -5, 0, -7, 6, 0, 0, -31, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -56, 0, 6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -36, 0, 35, 0, 0, -21, 0,
    19, 0, -39, -56, -39, -12, 17, 0,
    0, -39, 0, 7, -13, 0, -9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 15, 17, -70, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 27, 0, 4, 0, 0, 0,
    0, 0, 4, 4, -7, -12, 0, -2,
    -2, -6, 0, 0, -4, 0, 0, 0,
    -12, 0, -5, 0, -13, -12, 0, -14,
    -19, -19, -11, 0, -12, 0, -12, 0,
    0, 0, 0, -5, 0, 0, 6, 0,
    4, -6, 0, 2, 0, 0, 0, 6,
    -4, 0, 0, 0, -4, 6, 6, -2,
    0, 0, 0, -11, 0, -2, 0, 0,
    0, 0, 0, 2, 0, 7, -4, 0,
    -7, 0, -10, 0, 0, -4, 0, 17,
    0, 0, -6, 0, 0, 0, 0, 0,
    -2, 2, -4, -4, 0, 0, -6, 0,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, -3, 0, -6, -7, 0,
    0, 0, 0, 0, 2, 0, 0, -4,
    0, -6, -6, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -4, -7, 0, -9, 0, -17,
    -4, -17, 12, 0, 0, -12, 6, 12,
    16, 0, -14, -2, -7, 0, -2, -27,
    6, -4, 4, -31, 6, 0, 0, 2,
    -30, 0, -31, -5, -50, -4, 0, -29,
    0, 12, 16, 0, 7, 0, 0, 0,
    0, 1, 0, -10, -7, 0, -17, 0,
    0, 0, -6, 0, 0, 0, -6, 0,
    0, 0, 0, 0, -3, -3, 0, -3,
    -7, 0, 0, 0, 0, 0, 0, 0,
    -6, -6, 0, -4, -7, -5, 0, 0,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -5, 0, -7,
    0, -4, 0, -12, 6, 0, 0, -7,
    3, 6, 6, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 4,
    0, 0, -6, 0, -6, -4, -7, 0,
    0, 0, 0, 0, 0, 0, 5, 0,
    -5, 0, 0, 0, 0, -6, -9, 0,
    -11, 0, 17, -4, 2, -18, 0, 0,
    16, -29, -30, -24, -12, 6, 0, -5,
    -37, -10, 0, -10, 0, -12, 9, -10,
    -37, 0, -16, 0, 0, 3, -2, 5,
    -4, 0, 6, 1, -17, -22, 0, -29,
    -14, -12, -14, -17, -7, -16, -1, -11,
    -16, 3, 0, 2, 0, -6, 0, 0,
    0, 4, 0, 6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    0, -3, 0, -2, -6, 0, -10, -13,
    -13, -2, 0, -17, 0, 0, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 2,
    -3, 0, 0, 0, 6, 0, 0, 0,
    0, 0, 0, 0, 0, 28, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    -11, 0, 0, 0, 0, -29, -17, 0,
    0, 0, -9, -29, 0, 0, -6, 6,
    0, -16, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, -11,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 6, 0, -10, 0,
    0, 0, 0, 7, 0, 4, -12, -12,
    0, -6, -6, -7, 0, 0, 0, 0,
    0, 0, -17, 0, -6, 0, -9, -6,
    0, -13, -14, -17, -5, 0, -12, 0,
    -17, 0, 0, 0, 0, 46, 0, 0,
    3, 0, 0, -7, 0, 6, 0, -25,
    0, 0, 0, 0, 0, -54, -10, 19,
    17, -5, -24, 0, 6, -9, 0, -29,
    -3, -7, 6, -40, -6, 7, 0, 9,
    -20, -9, -21, -19, -24, 0, 0, -35,
    0, 33, 0, 0, -3, 0, 0, 0,
    -3, -3, -6, -16, -19, -1, -54, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -3, -6, -9, 0, 0,
    -12, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, 0, -12, 0, 0, 12,
    -2, 7, 0, -13, 6, -4, -2, -15,
    -6, 0, -7, -6, -4, 0, -9, -10,
    0, 0, -5, -2, -4, -10, -7, 0,
    0, -6, 0, 6, -4, 0, -13, 0,
    0, 0, -12, 0, -10, 0, -10, -10,
    6, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 6, 0, -8, 0, -4, -7,
    -18, -4, -4, -4, -2, -4, -7, -2,
    0, 0, 0, 0, 0, -6, -5, -5,
    0, 0, 0, 0, 7, -4, 0, -4,
    0, 0, 0, -4, -7, -4, -5, -7,
    -5, 0, 5, 23, -2, 0, -16, 0,
    -4, 12, 0, -6, -24, -7, 9, 1,
    0, -27, -10, 6, -10, 4, 0, -4,
    -5, -18, 0, -9, 3, 0, 0, -10,
    0, 0, 0, 6, 6, -12, -11, 0,
    -10, -6, -9, -6, -6, 0, -10, 3,
    -11, -10, 17, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, -7,
    0, 0, -6, -6, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    -9, 0, -12, 0, 0, 0, -19, 0,
    4, -13, 12, 1, -4, -27, 0, 0,
    -13, -6, 0, -23, -14, -16, 0, 0,
    -25, -6, -23, -22, -28, 0, -15, 0,
    5, 39, -7, 0, -13, -6, -2, -6,
    -10, -16, -10, -21, -24, -13, -6, 0,
    0, -4, 0, 2, 0, 0, -40, -5,
    17, 13, -13, -21, 0, 2, -18, 0,
    -29, -4, -6, 12, -53, -7, 2, 0,
    0, -37, -7, -30, -6, -42, 0, 0,
    -40, 0, 34, 2, 0, -4, 0, 0,
    0, 0, -3, -4, -22, -4, 0, -37,
    0, 0, 0, 0, -18, 0, -5, 0,
    -2, -16, -27, 0, 0, -3, -9, -17,
    -6, 0, -4, 0, 0, 0, 0, -26,
    -6, -19, -18, -5, -10, -14, -6, -10,
    0, -12, -5, -19, -9, 0, -7, -11,
    -6, -11, 0, 3, 0, -4, -19, 0,
    12, 0, -10, 0, 0, 0, 0, 7,
    0, 4, -12, 24, 0, -6, -6, -7,
    0, 0, 0, 0, 0, 0, -17, 0,
    -6, 0, -9, -6, 0, -13, -14, -17,
    -5, 0, -12, 5, 23, 0, 0, 0,
    0, 46, 0, 0, 3, 0, 0, -7,
    0, 6, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -4, -12, 0, 0, 0, 0, 0, -3,
    0, 0, 0, -6, -6, 0, 0, -12,
    -6, 0, 0, -12, 0, 10, -3, 0,
    0, 0, 0, 0, 0, 3, 0, 0,
    0, 0, 9, 12, 5, -5, 0, -18,
    -9, 0, 17, -19, -18, -12, -12, 23,
    10, 6, -50, -4, 12, -6, 0, -6,
    6, -6, -20, 0, -6, 6, -7, -5,
    -17, -5, 0, 0, 17, 12, 0, -16,
    0, -32, -7, 17, -7, -22, 2, -7,
    -19, -19, -6, 23, 6, 0, -9, 0,
    -16, 0, 5, 19, -13, -21, -23, -14,
    17, 0, 2, -42, -5, 6, -10, -4,
    -13, 0, -13, -21, -9, -9, -5, 0,
    0, -13, -12, -6, 0, 17, 13, -6,
    -32, 0, -32, -8, 0, -20, -33, -2,
    -18, -10, -19, -16, 16, 0, 0, -7,
    0, -12, -5, 0, -6, -10, 0, 10,
    -19, 6, 0, 0, -31, 0, -6, -13,
    -10, -4, -17, -14, -19, -13, 0, -17,
    -6, -13, -11, -17, -6, 0, 0, 2,
    27, -10, 0, -17, -6, 0, -6, -12,
    -13, -16, -16, -22, -7, -12, 12, 0,
    -9, 0, -29, -7, 3, 12, -18, -21,
    -12, -19, 19, -6, 3, -54, -10, 12,
    -13, -10, -21, 0, -17, -24, -7, -6,
    -5, -6, -12, -17, -2, 0, 0, 17,
    16, -4, -37, 0, -35, -13, 14, -22,
    -39, -12, -20, -24, -29, -19, 12, 0,
    0, 0, 0, -7, 0, 0, 6, -7,
    12, 4, -11, 12, 0, 0, -18, -2,
    0, -2, 0, 2, 2, -5, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 5, 17, 1, 0, -7, 0, 0,
    0, 0, -4, -4, -7, 0, 0, 0,
    2, 5, 0, 0, 0, 0, 5, 0,
    -5, 0, 22, 0, 10, 2, 2, -7,
    0, 12, 0, 0, 0, 5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 17, 0, 16, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -35, 0, -6, 10, 0, 17,
    0, 0, 57, 7, -12, -12, 6, 6,
    -4, 2, -29, 0, 0, 28, -35, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -39, 22, 81, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -35, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -9, 0, 0, -11,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, -16, 0,
    0, 2, 0, 0, 6, 74, -12, -5,
    18, 16, -16, 6, 0, 0, 6, 6,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -75, 16, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -16,
    0, 0, 0, -16, 0, 0, 0, 0,
    -13, -3, 0, 0, 0, -13, 0, -7,
    0, -27, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -39, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, -6, 0, 0, -11, 0, -9, 0,
    -16, 0, 0, 0, -10, 6, -7, 0,
    0, -16, -6, -13, 0, 0, -16, 0,
    -6, 0, -27, 0, -6, 0, 0, -47,
    -11, -23, -6, -21, 0, 0, -39, 0,
    -16, -3, 0, 0, 0, 0, 0, 0,
    0, 0, -9, -10, -5, -10, 0, 0,
    0, 0, -13, 0, -13, 7, -6, 12,
    0, -4, -13, -4, -10, -11, 0, -7,
    -3, -4, 4, -16, -2, 0, 0, 0,
    -51, -5, -8, 0, -13, 0, -4, -27,
    -5, 0, 0, -4, -5, 0, 0, 0,
    0, 4, 0, -4, -10, -4, 10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 7, 0, 0, 0, 0, 0,
    0, -13, 0, -4, 0, 0, 0, -12,
    6, 0, 0, 0, -16, -6, -12, 0,
    0, -16, 0, -6, 0, -27, 0, 0,
    0, 0, -56, 0, -12, -21, -29, 0,
    0, -39, 0, -4, -9, 0, 0, 0,
    0, 0, 0, 0, 0, -6, -9, -3,
    -9, 2, 0, 0, 10, -7, 0, 18,
    28, -6, -6, -17, 7, 28, 10, 13,
    -16, 7, 24, 7, 17, 13, 16, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 36, 27, -10, -6, 0, -5,
    46, 25, 46, 0, 0, 0, 6, 0,
    0, 21, 0, 0, -9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 8,
    0, 0, 0, 0, -48, -7, -5, -24,
    -28, 0, 0, -39, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    8, 0, 0, 0, 0, -48, -7, -5,
    -24, -28, 0, 0, -23, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, 0, -13, 6, 0, -6,
    5, 10, 6, -17, 0, -1, -5, 6,
    0, 5, 0, 0, 0, 0, -14, 0,
    -5, -4, -12, 0, -5, -23, 0, 36,
    -6, 0, -13, -4, 0, -4, -10, 0,
    -6, -16, -12, -7, 0, 0, 0, -9,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 0, 0, 0, -48,
    -7, -5, -24, -28, 0, 0, -39, 0,
    0, 0, 0, 0, 0, 29, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -9, 0, -18, -7, -5, 17, -5, -6,
    -23, 2, -3, 2, -4, -16, 1, 13,
    1, 5, 2, 5, -14, -23, -7, 0,
    -22, -11, -16, -24, -22, 0, -9, -12,
    -7, -7, -5, -4, -7, -4, 0, -4,
    -2, 9, 0, 9, -4, 0, 18, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, -6, -6, 0, 0,
    -16, 0, -3, 0, -10, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -35, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, -6, 0, -7,
    0, 0, 0, 0, -5, 0, 0, -10,
    -6, 6, 0, -10, -11, -4, 0, -17,
    -4, -13, -4, -7, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -39, 0, 18, 0, 0, -10, 0,
    0, 0, 0, -7, 0, -6, 0, 0,
    -3, 0, 0, -4, 0, -13, 0, 0,
    24, -7, -19, -18, 4, 6, 6, -1,
    -16, 4, 9, 4, 17, 4, 19, -4,
    -16, 0, 0, -23, 0, 0, -17, -16,
    0, 0, -12, 0, -7, -10, 0, -9,
    0, -9, 0, -4, 9, 0, -5, -17,
    -6, 21, 0, 0, -5, 0, -12, 0,
    0, 7, -13, 0, 6, -6, 5, 1,
    0, -19, 0, -4, -2, 0, -6, 6,
    -5, 0, 0, 0, -24, -7, -13, 0,
    -17, 0, 0, -27, 0, 21, -6, 0,
    -10, 0, 3, 0, -6, 0, -6, -17,
    0, -6, 6, 0, 0, 0, 0, -4,
    0, 0, 6, -7, 2, 0, 0, -7,
    -4, 0, -7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -36, 0, 13, 0,
    0, -5, 0, 0, 0, 0, 1, 0,
    -6, -6, 0, 0, 0, 12, 0, 13,
    0, 0, 0, 0, 0, -36, -33, 2,
    25, 17, 10, -23, 4, 24, 0, 21,
    0, 12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 31, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_36 = {
#else
lv_font_t lv_font_montserrat_36 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 40,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_36*/
